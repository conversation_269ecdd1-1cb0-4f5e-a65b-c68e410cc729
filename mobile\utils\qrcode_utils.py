"""
QR码工具模块 - 提供二维码生成和扫描功能。
该模块可以为用户创建动态二维码，并允许其他用户通过扫描二维码添加为家庭成员。
还支持通过二维码查看个人健康状况。
"""

import os
import json
import time
import uuid
import threading
import base64
from io import BytesIO
from datetime import datetime, timedelta

# 第三方库依赖
import qrcode
from PIL import Image, ImageDraw, ImageFont
from pyzbar import pyzbar
#import cv2
import numpy as np

# Kivy相关导入
from kivy.core.image import Image as CoreImage
from kivy.graphics.texture import Texture
from kivy.clock import Clock
from kivy.utils import platform

# 应用程序路径
MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
APP_DIR = os.path.dirname(MODULE_DIR)
TEMP_DIR = os.path.join(APP_DIR, "temp")

# 确保临时目录存在
if not os.path.exists(TEMP_DIR):
    os.makedirs(TEMP_DIR)

# QR码配置
class QRConfig:
    # 二维码数据类型
    TYPE_USER_INFO = "user_info"        # 用户基本信息
    TYPE_HEALTH_DATA = "health_data"    # 健康数据
    TYPE_ADD_MEMBER = "add_member"      # 添加成员
    
    # 二维码有效期（秒）
    DEFAULT_EXPIRY = 120  # 2分钟
    
    # 二维码样式参数
    QR_VERSION = 3                  # 二维码版本，1-40
    QR_SIZE = 300                   # 二维码尺寸像素
    QR_BORDER = 4                   # 二维码边距
    QR_LOGO_SIZE_RATIO = 0.25       # logo占二维码大小的比例
    
    # 健康状态颜色
    HEALTH_COLOR_NORMAL = (32, 184, 64)  # 绿色，健康
    HEALTH_COLOR_WARNING = (255, 205, 0)  # 黄色，警告
    HEALTH_COLOR_DANGER = (230, 0, 0)     # 红色，危险
    
    # 应用标识符
    APP_PREFIX = "HEALTH_TREA:"

# 二维码数据加密与解密
class QRCodeCrypto:
    @staticmethod
    def encrypt_data(data):
        """简单的数据"加密"，仅用于演示"""
        # 在实际应用中应使用真正的加密算法
        # 这里仅将数据转为JSON并进行Base64编码
        json_data = json.dumps(data, ensure_ascii=False)
        return base64.b64encode(json_data.encode('utf-8')).decode('utf-8')
    
    @staticmethod
    def decrypt_data(encrypted_data):
        """解密数据"""
        try:
            # 解码Base64并解析JSON
            decoded = base64.b64decode(encrypted_data).decode('utf-8')
            return json.loads(decoded)
        except Exception as e:
            print(f"解密数据失败: {e}")
            return None

# 二维码生成器
class QRCodeGenerator:
    @staticmethod
    def create_qr_data(type, user_data, expiry=None):
        """创建二维码数据"""
        expiry_time = int(time.time() + (expiry or QRConfig.DEFAULT_EXPIRY))
        
        qr_data = {
            "type": type,
            "data": user_data,
            "expiry": expiry_time,
            "uuid": str(uuid.uuid4())
        }
        
        # 添加应用程序标识前缀，确保只有本应用能识别
        return f"{QRConfig.APP_PREFIX}{QRCodeCrypto.encrypt_data(qr_data)}"
    
    @staticmethod
    def create_user_info_qr(user_data, expiry=None, logo_path=None):
        """生成包含用户信息的二维码"""
        qr_data = QRCodeGenerator.create_qr_data(
            QRConfig.TYPE_USER_INFO, 
            user_data, 
            expiry
        )
        return QRCodeGenerator.generate_qr_image(qr_data, logo_path)
    
    @staticmethod
    def create_health_data_qr(health_data, expiry=None, logo_path=None, health_status="normal"):
        """生成包含健康数据的二维码"""
        qr_data = QRCodeGenerator.create_qr_data(
            QRConfig.TYPE_HEALTH_DATA, 
            health_data, 
            expiry
        )
        
        # 根据健康状态选择颜色
        if health_status == "warning":
            color = QRConfig.HEALTH_COLOR_WARNING
        elif health_status == "danger":
            color = QRConfig.HEALTH_COLOR_DANGER
        else:
            color = QRConfig.HEALTH_COLOR_NORMAL
                
        return QRCodeGenerator.generate_qr_image(qr_data, logo_path, color)
    
    @staticmethod
    def create_add_member_qr(custom_id, user_name, expiry=None, logo_path=None):
        """生成添加成员的二维码"""
        member_data = {
            "user_id": custom_id,
            "name": user_name,
            "timestamp": int(time.time())
        }
        
        qr_data = QRCodeGenerator.create_qr_data(
            QRConfig.TYPE_ADD_MEMBER, 
            member_data, 
            expiry
        )
        return QRCodeGenerator.generate_qr_image(qr_data, logo_path)
    
    @staticmethod
    def generate_qr_image(data, logo_path=None, color=(0, 0, 0)):
        """生成QR码图像"""
        try:
            # 导入PIL图像工厂以确保可用性
            from qrcode.image.pil import PilImage

            qr = qrcode.QRCode(
                version=QRConfig.QR_VERSION,
                error_correction=qrcode.constants.ERROR_CORRECT_H,
                box_size=10,
                border=QRConfig.QR_BORDER,
                image_factory=PilImage  # 显式设置image_factory
            )
            
            print(f"添加数据到QR码: {data[:50]}...")  # 只显示前50个字符
            qr.add_data(data)
            print("调用qr.make(fit=True)...")
            qr.make(fit=True)
            print(f"QR码制作完成，模块数量: {qr.modules_count if hasattr(qr, 'modules_count') else '未知'}")
            
            # 创建带颜色的QR码图像 - 使用更稳定的方式
            try:
                print("开始生成QR码图像...")
                
                # 处理颜色参数 - 更健壮的颜色转换
                try:
                    if isinstance(color, tuple) and len(color) == 3:
                        # 确保颜色值在有效范围内
                        r = max(0, min(255, int(color[0])))
                        g = max(0, min(255, int(color[1])))
                        b = max(0, min(255, int(color[2])))
                        color_hex = "#{:02x}{:02x}{:02x}".format(r, g, b)
                        print(f"使用颜色: {color_hex}")
                    elif isinstance(color, str):
                        color_hex = color
                        print(f"使用字符串颜色: {color_hex}")
                    else:
                        color_hex = "black"
                        print("使用默认黑色")
                except Exception as color_error:
                    print(f"颜色转换失败: {color_error}，使用默认黑色")
                    color_hex = "black"
                
                print(f"QR对象状态 - version: {qr.version}, modules_count: {getattr(qr, 'modules_count', 'N/A')}")
                print(f"QR对象类型: {type(qr)}")
                print(f"QR对象是否有make_image方法: {hasattr(qr, 'make_image')}")
                
                # 检查qr对象的modules属性
                if hasattr(qr, 'modules') and qr.modules:
                    print(f"QR modules存在，长度: {len(qr.modules)}")
                else:
                    print("QR modules不存在或为空")
                
                # 尝试生成QR图像 - 使用多重安全策略
                print("调用qr.make_image...")
                qr_img_raw = None

                # 确保image_factory可用
                from qrcode.image.pil import PilImage
                if qr.image_factory is None:
                    print("image_factory为None，设置为PilImage")
                    qr.image_factory = PilImage

                # 策略1: 显式指定image_factory和颜色参数
                try:
                    print("策略1: 显式指定image_factory和颜色")
                    qr_img_raw = qr.make_image(
                        image_factory=PilImage,
                        fill_color=color_hex,
                        back_color="white"
                    )
                    print(f"策略1成功: {qr_img_raw}, 类型: {type(qr_img_raw)}")
                except Exception as strategy1_error:
                    print(f"策略1失败: {strategy1_error}")

                # 策略2: 只指定image_factory，不带颜色
                if qr_img_raw is None:
                    try:
                        print("策略2: 只指定image_factory")
                        qr_img_raw = qr.make_image(image_factory=PilImage)
                        print(f"策略2成功: {qr_img_raw}, 类型: {type(qr_img_raw)}")
                    except Exception as strategy2_error:
                        print(f"策略2失败: {strategy2_error}")

                # 策略3: 使用对象的image_factory
                if qr_img_raw is None:
                    try:
                        print("策略3: 使用对象的image_factory")
                        qr_img_raw = qr.make_image(fill_color=color_hex, back_color="white")
                        print(f"策略3成功: {qr_img_raw}, 类型: {type(qr_img_raw)}")
                    except Exception as strategy3_error:
                        print(f"策略3失败: {strategy3_error}")

                # 策略4: 最基本的调用
                if qr_img_raw is None:
                    try:
                        print("策略4: 最基本调用")
                        qr_img_raw = qr.make_image()
                        print(f"策略4成功: {qr_img_raw}, 类型: {type(qr_img_raw)}")
                    except Exception as strategy4_error:
                        print(f"策略4失败: {strategy4_error}")

                if qr_img_raw is None:
                    raise ValueError("所有make_image策略都失败")

                # 提取真正的PIL图像对象
                qr_img = None
                if hasattr(qr_img_raw, '_img'):
                    print("检测到qrcode.PilImage对象，提取内部PIL图像")
                    qr_img = qr_img_raw._img
                    print(f"提取后的PIL图像: {qr_img}, 类型: {type(qr_img)}")
                elif hasattr(qr_img_raw, 'get_image'):
                    print("检测到qrcode图像对象，调用get_image()")
                    qr_img = qr_img_raw.get_image()
                    print(f"get_image()结果: {qr_img}, 类型: {type(qr_img)}")
                else:
                    # 假设已经是PIL图像
                    qr_img = qr_img_raw
                    print(f"假设为PIL图像: {qr_img}, 类型: {type(qr_img)}")

                # 验证PIL图像对象
                if qr_img is None:
                    raise ValueError("提取PIL图像失败")

                if not hasattr(qr_img, 'convert'):
                    raise ValueError("不是有效的PIL图像对象")

                # 转换为RGBA模式
                print("转换为RGBA模式...")
                qr_img = qr_img.convert('RGBA')
                print(f"转换后图像类型: {type(qr_img)}, 尺寸: {qr_img.size}")

                # 最终验证
                if qr_img is None:
                    raise ValueError("图像转换为RGBA后变为None")
                
            except Exception as e:
                print(f"使用PIL图像工厂失败: {e}")
                # 回退到基本方式 - 使用相同的安全策略
                try:
                    print("回退到基本方式生成QR码...")
                    from qrcode.image.pil import PilImage

                    # 尝试多种回退策略
                    qr_img_fallback = None

                    # 回退策略1: 显式image_factory
                    try:
                        qr_img_fallback = qr.make_image(
                            image_factory=PilImage,
                            fill_color="black",
                            back_color="white"
                        )
                        print(f"回退策略1成功: {qr_img_fallback}, 类型: {type(qr_img_fallback)}")
                    except Exception as fallback1_error:
                        print(f"回退策略1失败: {fallback1_error}")

                    # 回退策略2: 最基本调用
                    if qr_img_fallback is None:
                        try:
                            qr_img_fallback = qr.make_image(image_factory=PilImage)
                            print(f"回退策略2成功: {qr_img_fallback}, 类型: {type(qr_img_fallback)}")
                        except Exception as fallback2_error:
                            print(f"回退策略2失败: {fallback2_error}")

                    if qr_img_fallback is None:
                        raise ValueError("所有回退策略都失败")

                    qr_img = qr_img_fallback
                    
                    # 检查是否是qrcode.image.pil.PilImage对象，需要转换为PIL.Image
                    if hasattr(qr_img, '_img'):
                        print("基本方式: 检测到qrcode.PilImage对象，提取内部PIL图像")
                        qr_img = qr_img._img
                        print(f"基本方式: 提取后的PIL图像: {qr_img}, 类型: {type(qr_img)}")
                    elif hasattr(qr_img, 'get_image'):
                        print("基本方式: 检测到qrcode图像对象，调用get_image()")
                        qr_img = qr_img.get_image()
                        print(f"基本方式: get_image()结果: {qr_img}, 类型: {type(qr_img)}")
                    
                    # 验证图像对象是否有效
                    if not hasattr(qr_img, 'convert'):
                        raise ValueError("生成的图像对象无效")
                    qr_img = qr_img.convert('RGBA')
                    print(f"基本方式: 转换为RGBA后: {type(qr_img)}")
                    # 再次验证转换后的图像
                    if qr_img is None:
                        raise ValueError("图像转换后为None")
                except Exception as inner_e:
                    print(f"基础QR图像生成失败: {inner_e}")
                    print(f"qr对象详细信息: version={getattr(qr, 'version', 'None')}, border={getattr(qr, 'border', 'None')}, box_size={getattr(qr, 'box_size', 'None')}")
                    raise ValueError(f"所有QR生成方法都失败: {inner_e}")
                
        except Exception as e:
            print(f"QR码生成完全失败: {e}")
            print(f"数据长度: {len(data) if data else 'None'}")
            print(f"QR对象最终状态: {getattr(qr, 'version', 'None') if 'qr' in locals() else 'qr对象未创建'}")
            # 创建一个简单的占位符图像
            try:
                print("开始创建占位符图像...")
                # 直接使用已导入的PIL模块
                qr_img = Image.new('RGBA', (200, 200), (255, 255, 255, 255))
                print(f"占位符图像创建成功: {type(qr_img)}")
                
                # 验证图像对象
                if qr_img is None:
                    print("无法创建占位符图像")
                    return None
                    
                # 在占位符上绘制错误标识
                try:
                    print("开始绘制占位符内容...")

                    # 验证图像对象有效性
                    if not hasattr(qr_img, 'size'):
                        print("占位符图像对象无效，跳过绘制")
                    else:
                        draw = ImageDraw.Draw(qr_img)
                        print(f"ImageDraw对象创建: {type(draw)}")

                        if draw is None:
                            print("ImageDraw对象为None，跳过绘制")
                        else:
                            # 绘制边框
                            try:
                                draw.rectangle([10, 10, 190, 190], outline=(255, 0, 0, 255), width=3)
                                print("绘制边框成功")
                            except Exception as rect_error:
                                print(f"绘制边框失败: {rect_error}")

                            # 尝试绘制文本
                            try:
                                draw.text((50, 90), "QR Error", fill=(255, 0, 0, 255))
                                print("绘制文本成功")
                            except Exception as text_error:
                                print(f"绘制文本失败: {text_error}")
                                # 文本绘制失败不影响整体功能

                except Exception as draw_error:
                    print(f"绘制占位符内容失败: {draw_error}")
                    # 即使绘制失败，也返回空白图像
                    
            except Exception as placeholder_error:
                print(f"创建占位符图像也失败: {placeholder_error}")
                # 最后的回退方案：返回None，让调用者处理
                return None
        
        # 如果提供了logo，则添加到QR码中心
        if logo_path and os.path.exists(logo_path):
            try:
                print(f"开始处理logo: {logo_path}")
                
                # 打开logo图像
                logo = Image.open(logo_path).convert('RGBA')
                print(f"logo图像加载成功: {type(logo)}")
                
                # 调整logo大小
                qr_width, qr_height = qr_img.size
                logo_size = int(min(qr_width, qr_height) * QRConfig.QR_LOGO_SIZE_RATIO)
                print(f"调整logo大小: {logo_size}x{logo_size}")
                logo = logo.resize((logo_size, logo_size), Image.LANCZOS)
                
                # 计算logo位置（居中）
                logo_pos = ((qr_width - logo_size) // 2, (qr_height - logo_size) // 2)
                
                # 创建一个新图像用于合成
                print("开始合成logo和QR码...")
                combined = Image.new('RGBA', qr_img.size, (255, 255, 255, 0))
                combined.paste(qr_img, (0, 0))
                combined.paste(logo, logo_pos, logo)
                print("logo合成成功")
                
                return combined
            except Exception as e:
                print(f"添加Logo失败: {e}")
        
        return qr_img
    
    @staticmethod
    def get_qr_texture(qr_image):
        """将PIL图像转换为Kivy纹理"""
        if qr_image is None:
            print("QR图像为None，无法创建纹理")
            return None
            
        try:
            # 保存图像到内存缓冲区
            buf = BytesIO()
            qr_image.save(buf, format='PNG')
            buf.seek(0)
            
            # 转换为Kivy纹理
            data = buf.read()
            buf.close()
            
            # 使用CoreImage创建纹理
            core_img = CoreImage(BytesIO(data), ext='png')
            texture = core_img.texture
            
            return texture
        except Exception as e:
            print(f"创建QR纹理失败: {e}")
            return None
    
    @staticmethod
    def save_qr_image(qr_image, filename=None):
        """保存QR码图像到文件"""
        if filename is None:
            filename = f"qrcode_{int(time.time())}.png"
        
        # 确保目录存在
        file_path = os.path.join(TEMP_DIR, filename)
        qr_image.save(file_path)
        
        return file_path

# 二维码扫描器
class QRCodeScanner:
    def __init__(self, callback=None):
        """初始化扫描器"""
        try:
            import cv2
            self.cv2 = cv2
        except ImportError:
            self.cv2 = None
            print("未安装 OpenCV，二维码扫描功能不可用")
        self.camera = None
        self.callback = callback
        self.scanning = False
        self.last_scan_time = 0
        self.scan_interval = 0.5  # 扫描间隔（秒）
        self.scan_thread = None

    def start_scanning(self, camera_index=0):
        """开始扫描二维码"""
        if self.scanning:
            return False
        if not self.cv2:
            print("未安装 OpenCV，无法启动扫描")
            return False
        try:
            self.camera = self.cv2.VideoCapture(camera_index)
            if not self.camera or not self.camera.isOpened():
                print("无法打开摄像头")
                return False
            self.scanning = True
            self.scan_thread = threading.Thread(target=self._scan_loop)
            self.scan_thread.daemon = True
            self.scan_thread.start()
            return True
        except Exception as e:
            print(f"启动扫描失败: {e}")
            return False

    def stop_scanning(self):
        """停止扫描"""
        self.scanning = False
        if self.scan_thread:
            self.scan_thread.join(timeout=1.0)
        if self.camera and hasattr(self.camera, 'release'):
            self.camera.release()
            self.camera = None

    def _scan_loop(self):
        """扫描循环"""
        while self.scanning:
            current_time = time.time()
            if current_time - self.last_scan_time < self.scan_interval:
                time.sleep(0.1)
                continue
            if self.camera and hasattr(self.camera, 'read'):
                ret, frame = self.camera.read()
                if not ret:
                    continue
                qr_data = self._decode_frame(frame)
                if qr_data:
                    self.last_scan_time = current_time
                    callback_func = self.callback
                    if callback_func is not None:
                        def schedule_callback(dt, data=qr_data):
                            if callback_func is not None:
                                callback_func(data)
                        Clock.schedule_once(schedule_callback, 0)

    def _decode_frame(self, frame):
        """解码帧中的二维码"""
        if not self.cv2:
            print("未安装 OpenCV，无法解码帧")
            return None
        gray = self.cv2.cvtColor(frame, self.cv2.COLOR_BGR2GRAY)
        decoded_objects = pyzbar.decode(gray)
        for obj in decoded_objects:
            qr_text = obj.data.decode('utf-8')
            if qr_text.startswith(QRConfig.APP_PREFIX):
                encrypted_data = qr_text[len(QRConfig.APP_PREFIX):]
                data = QRCodeCrypto.decrypt_data(encrypted_data)
                if data:
                    if data.get("expiry", 0) > time.time():
                        return data
                    else:
                        print("二维码已过期")
        return None

    @staticmethod
    def scan_from_image(image_path):
        """从图像文件扫描二维码"""
        try:
            try:
                import cv2
            except ImportError:
                print("未安装 OpenCV，无法扫描图像文件")
                return {"error": "未安装 OpenCV，无法扫描二维码"}
            # 读取图像
            if isinstance(image_path, str):
                image = cv2.imread(image_path)
            else:
                image = cv2.cvtColor(np.array(image_path), cv2.COLOR_RGB2BGR)
            if image is None:
                return {"error": "无法读取图像文件"}
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            decoded_objects = pyzbar.decode(gray)
            for obj in decoded_objects:
                qr_text = obj.data.decode('utf-8')
                if qr_text.startswith(QRConfig.APP_PREFIX):
                    encrypted_data = qr_text[len(QRConfig.APP_PREFIX):]
                    data = QRCodeCrypto.decrypt_data(encrypted_data)
                    if data:
                        if data.get("expiry", 0) > time.time():
                            return data
                        else:
                            return {"error": "二维码已过期"}
            return {"error": "未找到有效的二维码"}
        except Exception as e:
            print(f"扫描图像失败: {e}")
            return {"error": f"扫描图像失败: {str(e)}"}

# 动态二维码管理
class DynamicQRCode:
    def __init__(self, user_id, update_interval=30):
        """初始化动态二维码管理器"""
        self.user_id = user_id
        self.update_interval = update_interval  # 更新间隔（秒）
        self.qr_image = None
        self.qr_texture = None
        self.update_event = None
        self.user_data = None
        self.health_data = None
        self.logo_path = None
        self.callback = None
        self._updating = False  # 防止重复更新的标志
    
    def start(self, user_data, health_data=None, logo_path=None, callback=None):
        """开始动态二维码更新"""
        self.user_data = user_data
        self.health_data = health_data
        self.logo_path = logo_path
        self.callback = callback

        # 停止之前的更新
        self.stop()

        # 初始生成二维码 - 直接在主线程中执行
        Clock.schedule_once(self._safe_update_qr, 0)

        # 设置定时更新
        self.update_event = Clock.schedule_interval(
            self._safe_update_qr,
            self.update_interval
        )
    
    def stop(self):
        """停止动态二维码更新"""
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None
    
    def _safe_update_qr(self, dt=0):
        """安全的二维码更新方法 - 防止重复调用"""
        if self._updating:
            print("二维码正在更新中，跳过本次调用")
            return

        self._updating = True
        try:
            self._update_qr()
        finally:
            self._updating = False

    def _update_qr(self):
        """更新二维码 - 确保在主线程中执行"""
        try:
            print(f"开始更新二维码，当前线程: {threading.current_thread().name}")
            
            if self.health_data:
                # 确定健康状态
                health_status = self._calculate_health_status(self.health_data)
                
                # 生成带健康状态的二维码
                self.qr_image = QRCodeGenerator.create_health_data_qr(
                    self.health_data,
                    expiry=self.update_interval + 10,  # 有效期略长于更新间隔
                    logo_path=self.logo_path,
                    health_status=health_status
                )
            else:
                # 生成用户信息二维码
                self.qr_image = QRCodeGenerator.create_user_info_qr(
                    self.user_data,
                    expiry=self.update_interval + 10,
                    logo_path=self.logo_path
                )
            
            # 更新纹理
            if self.qr_image is not None:
                self.qr_texture = QRCodeGenerator.get_qr_texture(self.qr_image)
                print("QR图像和纹理生成成功")
            else:
                print("QR图像生成失败，无法创建纹理")
                self.qr_texture = None
            
            # 调用回调函数更新UI - 确保在主线程中执行
            if self.callback and callable(self.callback):
                def safe_callback(dt):
                    try:
                        # 即使纹理为None也要调用回调，让UI知道更新失败
                        self.callback(self.qr_texture)
                        print("二维码回调函数调用成功")
                    except Exception as e:
                        print(f"二维码回调函数调用失败: {e}")
                        import traceback
                        traceback.print_exc()
                
                # 使用Clock.schedule_once确保回调在主线程中执行
                Clock.schedule_once(safe_callback, 0)
                
        except Exception as e:
            print(f"二维码更新失败: {e}")
            import traceback
            traceback.print_exc()
            # 即使失败也要通知UI
            if self.callback and callable(self.callback):
                def error_callback(dt):
                    try:
                        self.callback(None)
                    except Exception as cb_e:
                        print(f"错误回调失败: {cb_e}")
                Clock.schedule_once(error_callback, 0)
    
    def _calculate_health_status(self, health_data):
        """根据健康数据计算健康状态"""
        # 这里是简化的健康状态计算，实际应用应根据医学标准判断
        danger_count = 0
        warning_count = 0
        
        # 检查血压
        if 'blood_pressure' in health_data:
            systolic = health_data['blood_pressure'].get('systolic', 120)
            diastolic = health_data['blood_pressure'].get('diastolic', 80)
            
            if systolic > 180 or diastolic > 110:
                danger_count += 1
            elif systolic > 140 or diastolic > 90:
                warning_count += 1
        
        # 检查血糖
        if 'blood_glucose' in health_data:
            glucose = health_data['blood_glucose'].get('value', 5.5)
            
            if glucose > 11.1:
                danger_count += 1
            elif glucose > 7.0:
                warning_count += 1
        
        # 根据危险和警告计数确定状态
        if danger_count > 0:
            return "danger"
        elif warning_count > 0:
            return "warning"
        else:
            return "normal"
    
    def get_current_texture(self):
        """获取当前二维码纹理"""
        if not self.qr_texture:
            self._update_qr()
        return self.qr_texture
    
    def save_current_image(self, filename=None):
        """保存当前二维码图像"""
        if not self.qr_image:
            self._update_qr()
        
        return QRCodeGenerator.save_qr_image(self.qr_image, filename)

# 便捷函数
def generate_user_qrcode(user_data, logo_path=None):
    """生成用户信息二维码"""
    return QRCodeGenerator.create_user_info_qr(user_data, logo_path=logo_path)

def generate_health_qrcode(user_data, health_data, logo_path=None):
    """生成健康信息二维码"""
    return QRCodeGenerator.create_health_data_qr(health_data, logo_path=logo_path)

def generate_member_invitation_qrcode(user_id, user_name, logo_path=None):
    """生成家庭成员邀请二维码"""
    return QRCodeGenerator.create_add_member_qr(user_id, user_name, logo_path=logo_path)

def scan_qrcode_from_image(image_path):
    """从图像文件扫描二维码"""
    return QRCodeScanner.scan_from_image(image_path)