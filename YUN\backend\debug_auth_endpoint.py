#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试认证端点问题
"""

import sys
import os
import requests
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_auth_endpoint():
    """
    测试认证端点
    """
    base_url = "http://localhost:8006"
    
    print(f"开始调试认证端点 - {datetime.now()}")
    print("=" * 60)
    
    # 测试数据
    test_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    # 测试不同的端点
    endpoints = [
        "/api/auth/register/login",
        "/api/auth/login",
        "/api/auth/frontend_login",
        "/api/auth/simple_login",
        "/auth/login"
    ]
    
    for endpoint in endpoints:
        print(f"\n测试端点: {endpoint}")
        print("-" * 40)
        
        try:
            url = f"{base_url}{endpoint}"
            print(f"请求URL: {url}")
            print(f"请求数据: {json.dumps(test_data, indent=2)}")
            print(f"请求头: {json.dumps(headers, indent=2)}")
            
            # 发送请求
            response = requests.post(
                url,
                json=test_data,
                headers=headers,
                timeout=10
            )
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            # 尝试解析响应
            try:
                if response.headers.get('content-type', '').startswith('application/json'):
                    response_data = response.json()
                    print(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                else:
                    print(f"响应内容: {response.text}")
            except Exception as e:
                print(f"解析响应失败: {e}")
                print(f"原始响应: {response.text}")
                
            # 检查是否成功
            if response.status_code == 200:
                print("✅ 请求成功")
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        except Exception as e:
            print(f"❌ 其他异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"调试完成 - {datetime.now()}")

def test_server_status():
    """
    测试服务器状态
    """
    base_url = "http://localhost:8006"
    
    print("\n检查服务器状态...")
    print("-" * 30)
    
    # 测试基本端点
    test_endpoints = [
        "/",
        "/health",
        "/api/health",
        "/api/docs"
    ]
    
    for endpoint in test_endpoints:
        try:
            url = f"{base_url}{endpoint}"
            response = requests.get(url, timeout=5)
            print(f"{endpoint:20} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"{endpoint:20} - 错误: {e}")

if __name__ == "__main__":
    test_server_status()
    test_auth_endpoint()