# -*- coding: utf-8 -*-
"""
离线同步工具：提供简单的本地队列、合并与指数退避重试机制。
注意：为了安全，本模块仅在移动端App中使用，不会删除或修改服务器数据。
"""
from __future__ import annotations
import json
import time
from pathlib import Path
from typing import Dict, Any, List

DEFAULT_PATH = Path.home() / ".health_management" / "queue" / "sync_tasks.json"
DEFAULT_PATH.parent.mkdir(parents=True, exist_ok=True)

class OfflineQueue:
    def __init__(self, path: Path = DEFAULT_PATH):
        self.path = Path(path)
        self.path.parent.mkdir(parents=True, exist_ok=True)
        self._load()

    def _load(self):
        if self.path.exists():
            try:
                self.data = json.loads(self.path.read_text(encoding="utf-8"))
            except Exception:
                self.data = {"tasks": []}
        else:
            self.data = {"tasks": []}

    def _save(self):
        self.path.write_text(json.dumps(self.data, ensure_ascii=False, indent=2), encoding="utf-8")

    def enqueue(self, user: str, table: str, record_id: int, op: str, payload: Dict[str, Any] | None = None):
        ts = int(time.time())
        item = {"user": user, "table": table, "record_id": record_id, "op": op, "payload": payload or {}, "ts": ts, "retry": 0}
        # 合并策略：同一 record_id 只保留最后一次操作（create/update/delete/stop/reminder）
        self.data["tasks"] = [t for t in self.data["tasks"] if not (t["user"]==user and t["table"]==table and t["record_id"]==record_id)]
        self.data["tasks"].append(item)
        self._save()

    def list(self) -> List[Dict[str, Any]]:
        return list(self.data.get("tasks", []))

    def clear(self):
        self.data = {"tasks": []}
        self._save()

    def pop(self) -> Dict[str, Any] | None:
        if not self.data.get("tasks"):
            return None
        t = self.data["tasks"].pop(0)
        self._save()
        return t

    def push_front(self, task: Dict[str, Any]):
        self.data.setdefault("tasks", [])
        self.data["tasks"].insert(0, task)
        self._save()

    @staticmethod
    def next_delay(retry: int) -> float:
        # 指数退避：1s, 2s, 4s, 最长 60s
        return min(60.0, 2 ** max(0, retry))

