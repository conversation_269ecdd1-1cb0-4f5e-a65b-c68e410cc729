"""
时间选择器工具模块
为KivyMD 2.0.1dev0提供统一的时间选择功能

用法:
    from utils.time_picker_utils import show_time_picker
    show_time_picker(lambda t: print(t.strftime("%H:%M")))

回调参数:
    - selected_time: 优先为 datetime.time 或 datetime.datetime, 可直接使用 strftime("%H:%M").
      在降级方案中, 解析失败时可能返回当前时间。
"""
from __future__ import annotations

from datetime import datetime, time
from typing import Callable, Optional

from kivy.logger import Logger as KivyLogger


class TimePickerManager:
    """时间选择器管理器

    优先使用 KivyMD 2.0.1dev0 的 MDTimePicker, 不可用时回退到文本输入对话框。
    """

    def __init__(self) -> None:
        self.current_picker = None
        self.callback_function: Optional[Callable[[time | datetime], None]] = None

    def show_time_picker(
        self,
        callback_function: Callable[[time | datetime], None],
        default_time: Optional[time | datetime] = None,
        title: str = "选择时间",
    ) -> None:
        """显示时间选择器

        Args:
            callback_function: 选中时间后的回调函数
            default_time: 默认时间 (datetime.time 或 datetime)
            title: 对话框标题(部分实现可能未显示)
        """
        try:
            self._show_modern_time_picker(callback_function, default_time, title)
        except ImportError:
            self._show_fallback_time_picker(callback_function, default_time, title)
        except Exception as e:  # 最终兜底
            KivyLogger.error(f"TimePicker: 显示时间选择器失败: {e}")
            # 直接回调当前时间，避免阻塞业务流程
            try:
                now = datetime.now()
                callback_function(now)
            except Exception as cb_e:
                KivyLogger.error(f"TimePicker: 回调失败: {cb_e}")

    def _show_modern_time_picker(
        self,
        callback_function: Callable[[time | datetime], None],
        default_time: Optional[time | datetime],
        title: str,
    ) -> None:
        """使用 KivyMD 2.0.1dev0 的 MDTimePicker"""
        # 兼容多种导入路径
        try:
            from kivymd.uix.pickers import MDTimePicker  # type: ignore
        except ImportError:
            try:
                from kivymd.uix.pickers.timepicker import MDTimePicker  # type: ignore
            except ImportError:
                try:
                    from kivymd.uix.picker import MDTimePicker  # 旧版本兼容
                except ImportError:
                    raise ImportError("无法导入MDTimePicker")

        self.callback_function = callback_function

        picker = MDTimePicker()
        self.current_picker = picker

        # 设置默认时间（尽力而为）
        try:
            if default_time is None:
                default_time = datetime.now().time()
            if hasattr(picker, "set_time"):
                # set_time 接受 datetime.time
                if isinstance(default_time, datetime):
                    picker.set_time(default_time.time())
                elif isinstance(default_time, time):
                    picker.set_time(default_time)
        except Exception as e:
            KivyLogger.warning(f"TimePicker: 设置默认时间失败: {e}")

        # 绑定事件（兼容不同版本事件名）
        bound = False
        try:
            # 一些实现使用 on_save 回调: (instance, time)
            picker.bind(on_save=self._on_time_save)
            bound = True
        except Exception:
            pass
        if not bound:
            try:
                # 也有实现直接提供 time 属性事件: (instance, time)
                picker.bind(time=self._on_time_selected)
                bound = True
            except Exception:
                pass
        if not bound:
            try:
                # 兜底: on_dismiss 时读取 picker.time
                picker.bind(on_dismiss=self._on_time_dismiss)
            except Exception as e:
                KivyLogger.warning(f"TimePicker: 事件绑定失败, 使用兜底: {e}")

        # 打开选择器
        try:
            picker.open()
        except Exception as e:
            KivyLogger.error(f"TimePicker: 打开时间选择器失败: {e}")
            raise ImportError("MDTimePicker 打开失败")

    def _on_time_save(self, instance, value):
        """on_save 回调"""
        try:
            if self.callback_function:
                # value 可能为 datetime.time 或 datetime
                self.callback_function(value)
        except Exception as e:
            KivyLogger.error(f"TimePicker: 处理 on_save 回调失败: {e}")
        finally:
            self._cleanup()

    def _on_time_selected(self, instance, value):
        """time 属性回调"""
        try:
            if self.callback_function:
                self.callback_function(value)
        except Exception as e:
            KivyLogger.error(f"TimePicker: 处理 time 回调失败: {e}")

    def _on_time_dismiss(self, instance):
        """on_dismiss 兜底回调"""
        try:
            if self.callback_function:
                try:
                    t = getattr(instance, "time", None)
                    if t is None:
                        t = datetime.now().time()
                    self.callback_function(t)
                except Exception:
                    self.callback_function(datetime.now())
        except Exception as e:
            KivyLogger.error(f"TimePicker: 处理 on_dismiss 回调失败: {e}")
        finally:
            self._cleanup()

    def _show_fallback_time_picker(
        self,
        callback_function: Callable[[time | datetime], None],
        default_time: Optional[time | datetime],
        title: str,
    ) -> None:
        """文本输入对话框降级方案 (HH:MM)"""
        try:
            from kivymd.uix.dialog import (
                MDDialog,
                MDDialogHeadlineText,
                MDDialogSupportingText,
                MDDialogButtonContainer,
            )
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.textfield import MDTextField, MDTextFieldHintText
        except Exception as e:
            KivyLogger.error(f"TimePicker: 导入降级组件失败: {e}")
            raise

        self.callback_function = callback_function
        # 预填默认时间
        default_str = ""
        try:
            if default_time is None:
                default_time = datetime.now().time()
            if isinstance(default_time, datetime):
                default_str = default_time.strftime("%H:%M")
            elif isinstance(default_time, time):
                default_str = default_time.strftime("%H:%M")
            else:
                default_str = str(default_time)
        except Exception:
            default_str = datetime.now().strftime("%H:%M")

        time_input = MDTextField(
            text=default_str,
            mode="outlined",
            size_hint_y=None,
            height=48,
        )
        time_input.add_widget(MDTextFieldHintText(text="时间 (HH:MM)"))

        def _confirm(*_):
            try:
                value = time_input.text.strip()
                hh, mm = value.split(":")
                t = time(hour=int(hh), minute=int(mm))
                if self.callback_function:
                    self.callback_function(t)
                dialog.dismiss()
            except Exception:
                # 输入不合法则使用当前时间
                if self.callback_function:
                    self.callback_function(datetime.now().time())
                dialog.dismiss()

        dialog = MDDialog(
            MDDialogHeadlineText(text=title),
            MDDialogSupportingText(text="请输入时间，格式：HH:MM"),
            time_input,
            MDDialogButtonContainer(
                MDButton(MDButtonText(text="取消"), style="outlined", on_release=lambda *_: dialog.dismiss()),
                MDButton(MDButtonText(text="确认"), style="filled", on_release=_confirm),
            ),
            size_hint=(0.85, None),
        )

        try:
            dialog.open()
        except Exception as e:
            KivyLogger.error(f"TimePicker: 打开降级对话框失败: {e}")
            # 最后兜底: 直接回调当前时间
            if self.callback_function:
                self.callback_function(datetime.now())

    def _cleanup(self) -> None:
        try:
            self.current_picker = None
            self.callback_function = None
        except Exception:
            pass


_time_picker_manager: Optional[TimePickerManager] = None


def get_time_picker_manager() -> TimePickerManager:
    """获取全局时间选择器管理器实例"""
    global _time_picker_manager
    if _time_picker_manager is None:
        _time_picker_manager = TimePickerManager()
    return _time_picker_manager


def show_time_picker(
    callback_function: Callable[[time | datetime], None],
    default_time: Optional[time | datetime] = None,
    title: str = "选择时间",
) -> None:
    """便捷函数: 显示时间选择器"""
    manager = get_time_picker_manager()
    manager.show_time_picker(callback_function, default_time, title)

