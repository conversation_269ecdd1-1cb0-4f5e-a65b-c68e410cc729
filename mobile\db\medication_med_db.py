"""
独立的用药数据库管理模块（供单元测试和业务逻辑调用）
从 screens 模块抽离，避免引入 UI 与第三方依赖，便于 pytest 独立运行。
"""
from __future__ import annotations

import json
import logging
import sqlite3
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)

MEDICATIONS_TABLE = "medications"
REMINDERS_TABLE = "medication_reminders"


class MedicationDatabaseManager:
    """用药数据库管理器（SQLite）"""

    def __init__(self, db_path: str | None = None):
        if db_path is None:
            app_data_dir = Path.home() / ".health_management" / "data"
            app_data_dir.mkdir(parents=True, exist_ok=True)
            db_path = str(app_data_dir / "medications.db")
        self.db_path = str(db_path)
        self.init_database()

    def init_database(self) -> None:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS {MEDICATIONS_TABLE} (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        custom_id TEXT NOT NULL,
                        name TEXT NOT NULL,
                        dosage TEXT NOT NULL,
                        frequency TEXT NOT NULL,
                        start_date TEXT NOT NULL,
                        end_date TEXT,
                        reason TEXT,
                        notes TEXT,
                        status TEXT DEFAULT 'active',
                        stop_reason TEXT,
                        stop_date TEXT,
                        created_at TEXT NOT NULL,
                        updated_at TEXT NOT NULL,
                        encrypted_data TEXT
                    )
                ''')
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS {REMINDERS_TABLE} (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        medication_id INTEGER NOT NULL,
                        custom_id TEXT NOT NULL,
                        reminder_time TEXT NOT NULL,
                        reminder_type TEXT DEFAULT 'daily',
                        is_active BOOLEAN DEFAULT 1,
                        created_at TEXT NOT NULL,
                        FOREIGN KEY (medication_id) REFERENCES {MEDICATIONS_TABLE} (id)
                    )
                ''')
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_medications_custom_id ON {MEDICATIONS_TABLE} (custom_id)')
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_medications_status ON {MEDICATIONS_TABLE} (status)')
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_reminders_custom_id ON {REMINDERS_TABLE} (custom_id)')
                conn.commit()
                logger.info("[MedicationDB] 数据库初始化完成")
        except Exception as e:
            logger.error(f"[MedicationDB] 数据库初始化失败: {e}")
            raise

    def encrypt_sensitive_data(self, data: str) -> str:
        try:
            return hashlib.sha256(data.encode()).hexdigest()
        except Exception as e:
            logger.error(f"[MedicationDB] 数据加密失败: {e}")
            return data

    def save_medication(self, custom_id: str, medication_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()
                encrypted_notes = self.encrypt_sensitive_data(medication_data.get('notes', ''))
                cursor.execute(f'''
                    INSERT INTO {MEDICATIONS_TABLE}
                    (custom_id, name, dosage, frequency, start_date, end_date, reason, notes,
                     status, created_at, updated_at, encrypted_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    custom_id,
                    medication_data['name'],
                    medication_data['dosage'],
                    medication_data['frequency'],
                    medication_data['start_date'],
                    medication_data.get('end_date'),
                    medication_data.get('reason'),
                    medication_data.get('notes'),
                    'active',
                    now,
                    now,
                    encrypted_notes
                ))
                medication_id = cursor.lastrowid
                cursor.execute(f'''
                    SELECT id, custom_id, name, dosage, frequency, start_date, end_date,
                           reason, notes, status, created_at, updated_at
                    FROM {MEDICATIONS_TABLE}
                    WHERE id = ?
                ''', (medication_id,))
                row = cursor.fetchone()
                conn.commit()
                if row:
                    return {
                        'id': row[0], 'custom_id': row[1], 'name': row[2], 'dosage': row[3],
                        'frequency': row[4], 'start_date': row[5], 'end_date': row[6],
                        'reason': row[7], 'notes': row[8], 'status': row[9],
                        'created_at': row[10], 'updated_at': row[11]
                    }
        except Exception as e:
            logger.error(f"[MedicationDB] 保存用药记录失败: {e}")
        return None

    def update_medication_status(self, medication_id: int, status: str, stop_date: str | None = None, stop_reason: str | None = None) -> bool:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()
                if status == 'stopped':
                    formatted_stop_date = stop_date
                    if stop_date and 'T' in stop_date:
                        formatted_stop_date = stop_date.split('T')[0]
                    elif not stop_date:
                        formatted_stop_date = datetime.now().strftime("%Y-%m-%d")
                    cursor.execute(f'''
                        UPDATE {MEDICATIONS_TABLE}
                        SET status = ?, stop_date = ?, stop_reason = ?, updated_at = ?
                        WHERE id = ?
                    ''', (status, formatted_stop_date, stop_reason, now, medication_id))
                else:
                    cursor.execute(f'''
                        UPDATE {MEDICATIONS_TABLE}
                        SET status = ?, updated_at = ?
                        WHERE id = ?
                    ''', (status, now, medication_id))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"[MedicationDB] 更新药物状态失败: {e}")
            return False

    def delete_medication(self, medication_id: int) -> bool:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''DELETE FROM {MEDICATIONS_TABLE} WHERE id = ?''', (medication_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"[MedicationDB] 删除药物失败: {e}")
            return False

    def get_medications(self, custom_id: str, status: str = 'active') -> List[Dict[str, Any]]:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    SELECT * FROM {MEDICATIONS_TABLE}
                    WHERE custom_id = ? AND status = ?
                    ORDER BY created_at DESC
                ''', (custom_id, status))
                columns = [d[0] for d in cursor.description]
                medications: List[Dict[str, Any]] = []
                for row in cursor.fetchall():
                    m = dict(zip(columns, row))
                    mid = m.get('id')
                    m['reminder_settings'] = self._get_medication_reminders(mid) if mid else {}
                    medications.append(m)
                return medications
        except Exception as e:
            logger.error(f"[MedicationDB] 获取用药记录失败: {e}")
            return []

    def _get_medication_reminders(self, medication_id: int) -> Dict[str, Any]:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    SELECT reminder_time, reminder_type FROM {REMINDERS_TABLE}
                    WHERE medication_id = ? AND is_active = 1
                    ORDER BY created_at DESC
                ''', (medication_id,))
                reminders = cursor.fetchall()
                if not reminders:
                    return {}
                settings: Dict[str, Any] = {
                    'med_reminder': {'enabled': False, 'times': []},
                    'review_reminder': {'enabled': False}
                }
                for rtime, rtype in reminders:
                    try:
                        data = json.loads(rtime) if rtime and rtime.startswith('{') else {'time': rtime}
                        if rtype in ('medication', 'daily'):
                            settings['med_reminder']['enabled'] = True
                            if 'times' in data:
                                settings['med_reminder']['times'] = data['times']
                            elif 'time' in data:
                                settings['med_reminder']['times'].append({
                                    'time': data['time'],
                                    'advance_minutes': data.get('advance_minutes', '15')
                                })
                        elif rtype == 'review':
                            settings['review_reminder']['enabled'] = True
                            settings['review_reminder']['date'] = data.get('date', '')
                            settings['review_reminder']['advance_days'] = data.get('advance_days', '3')
                    except Exception as pe:
                        logger.error(f"[MedicationDB] 解析提醒失败: {pe}")
                        continue
                return settings
        except Exception as e:
            logger.error(f"[MedicationDB] 获取提醒设置失败: {e}")
            return {}

    def stop_medication(self, medication_id: int, stop_reason: str) -> bool:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                now = datetime.now()
                stop_date = now.strftime("%Y-%m-%d")
                updated_at = now.isoformat()
                cursor.execute(f'''
                    UPDATE {MEDICATIONS_TABLE}
                    SET status = 'stopped', stop_reason = ?, stop_date = ?, updated_at = ?
                    WHERE id = ?
                ''', (stop_reason, stop_date, updated_at, medication_id))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"[MedicationDB] 停用药物失败: {e}")
            return False

    def save_reminder(self, medication_id: int, custom_id: str, reminder_data: Dict[str, Any]) -> bool:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()
                reminder_type = reminder_data.get('reminder_type', 'daily')
                cursor.execute(f'''DELETE FROM {REMINDERS_TABLE} WHERE medication_id=? AND custom_id=? AND reminder_type=?''', (medication_id, custom_id, reminder_type))
                cursor.execute(f'''
                    INSERT INTO {REMINDERS_TABLE}
                    (medication_id, custom_id, reminder_time, reminder_type, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (medication_id, custom_id, reminder_data['reminder_time'], reminder_type, True, now))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"[MedicationDB] 保存提醒设置失败: {e}")
            return False

    def set_medication_reminder(self, medication_id: int, custom_id: str, reminder_time: str, reminder_type: str = 'daily') -> bool:
        try:
            return self.save_reminder(medication_id, custom_id, {
                'reminder_time': reminder_time,
                'reminder_type': reminder_type,
            })
        except Exception as e:
            logger.error(f"[MedicationDB] 设置用药提醒失败: {e}")
            return False

    def export_data(self, custom_id: str, export_path: str) -> bool:
        try:
            medications = self.get_medications(custom_id, status='active')
            history = self.get_medications(custom_id, status='stopped')
            payload = {
                'export_time': datetime.now().isoformat(),
                'custom_id': custom_id,
                'current_medications': medications,
                'history_medications': history,
                'total_count': len(medications) + len(history),
            }
            Path(export_path).write_text(json.dumps(payload, ensure_ascii=False, indent=2), encoding='utf-8')
            return True
        except Exception as e:
            logger.error(f"[MedicationDB] 数据导出失败: {e}")
            return False


_db_singleton: Optional[MedicationDatabaseManager] = None


def get_medication_db_manager() -> MedicationDatabaseManager:
    global _db_singleton
    if _db_singleton is None:
        _db_singleton = MedicationDatabaseManager()
    return _db_singleton

