2025-08-09 01:13:21,884 - INFO - HTTP请求日志初始化完成，日志文件: C:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250809.log
2025-08-09 01:13:21,885 - INFO - 已启用HTTP请求日志记录
2025-08-09 01:13:22,331 - DEBUG - [caa6ba36] HTTP请求开始 - GET None
2025-08-09 01:13:26,860 - ERROR - [caa6ba36] HTTP请求异常 - GET None - 异常: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/mobile/assessments?custom_id=SM_006&limit=20 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001ED3F2B4D70>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')) - 耗时: 4.381s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
        (self._dns_host, self.port),
    ...<2 lines>...
        socket_options=self.socket_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
    ~~~~~~~~~~~~^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
    ~~~~~~~~~~~~^
        method,
        ^^^^^^^
    ...<6 lines>...
        enforce_content_length=enforce_content_length,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 445, in request
    self.endheaders()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1333, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1093, in _send_output
    self.send(msg)
    ~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1037, in send
    self.connect()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 276, in connect
    self.sock = self._new_conn()
                ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
        self, f"Failed to establish a new connection: {e}"
    ) from e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001ED3F2B4D70>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/mobile/assessments?custom_id=SM_006&limit=20 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001ED3F2B4D70>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\health-Trea\mobile\utils\http_logger.py", line 290, in wrapper
    response = func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/mobile/assessments?custom_id=SM_006&limit=20 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001ED3F2B4D70>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

2025-08-09 01:13:26,861 - DEBUG - HTTP请求详情: {"request_id": "caa6ba36", "timestamp": "2025-08-09T01:13:22.331342", "method": "GET", "url": null, "params": {"custom_id": "SM_006", "limit": 20}, "headers": {"X-User-ID": "SM_006", "Content-Type": "application/json"}, "exception": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/mobile/assessments?custom_id=SM_006&limit=20 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001ED3F2B4D70>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))", "elapsed_time": "4.381s"}
2025-08-09 01:13:26,861 - DEBUG - [2a714ae6] HTTP请求开始 - GET None
2025-08-09 01:13:30,925 - ERROR - [2a714ae6] HTTP请求异常 - GET None - 异常: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/mobile/assessments?custom_id=SM_006&status=pending&limit=20 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001ED3F324CD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')) - 耗时: 4.060s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
        (self._dns_host, self.port),
    ...<2 lines>...
        socket_options=self.socket_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
    ~~~~~~~~~~~~^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
    ~~~~~~~~~~~~^
        method,
        ^^^^^^^
    ...<6 lines>...
        enforce_content_length=enforce_content_length,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 445, in request
    self.endheaders()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1333, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1093, in _send_output
    self.send(msg)
    ~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1037, in send
    self.connect()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 276, in connect
    self.sock = self._new_conn()
                ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
        self, f"Failed to establish a new connection: {e}"
    ) from e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001ED3F324CD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/mobile/assessments?custom_id=SM_006&status=pending&limit=20 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001ED3F324CD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\health-Trea\mobile\utils\http_logger.py", line 290, in wrapper
    response = func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/mobile/assessments?custom_id=SM_006&status=pending&limit=20 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001ED3F324CD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

2025-08-09 01:13:30,926 - DEBUG - HTTP请求详情: {"request_id": "2a714ae6", "timestamp": "2025-08-09T01:13:26.861797", "method": "GET", "url": null, "params": {"custom_id": "SM_006", "status": "pending", "limit": 20}, "headers": {"X-User-ID": "SM_006", "Content-Type": "application/json"}, "exception": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/mobile/assessments?custom_id=SM_006&status=pending&limit=20 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001ED3F324CD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))", "elapsed_time": "4.060s"}
2025-08-09 01:13:31,684 - DEBUG - [e6ec2fa5] HTTP请求开始 - POST None
2025-08-09 01:13:33,741 - INFO - [e6ec2fa5] HTTP响应 - POST None - 状态码: 200 - 耗时: 2.057s
2025-08-09 01:13:33,741 - DEBUG - HTTP请求详情: {"request_id": "e6ec2fa5", "timestamp": "2025-08-09T01:13:31.684773", "method": "POST", "url": null, "json": {"username": "SM_008", "password": "******"}, "status_code": 200, "elapsed_time": "2.057s", "response": "{\n  \"status\": \"error\",\n  \"message\": \"用户名或密码错误\"\n}"}
2025-08-09 01:13:33,742 - DEBUG - [81d036d7] HTTP请求开始 - GET None
2025-08-09 01:13:35,796 - WARNING - [81d036d7] HTTP响应 - GET None - 状态码: 422 - 耗时: 2.054s
2025-08-09 01:13:35,796 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "int_parsing",
      "loc": [
        "path",
        "record_id"
      ],
      "msg": "Input should be a valid integer, unable to parse string as an integer",
      "input": "SM_008"
    }
  ]
}
2025-08-09 01:13:35,797 - DEBUG - HTTP请求详情: {"request_id": "81d036d7", "timestamp": "2025-08-09T01:13:33.742203", "method": "GET", "url": null, "headers": {"Authorization": "********", "Content-Type": "application/json"}, "status_code": 422, "elapsed_time": "2.054s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"int_parsing\",\n      \"loc\": [\n        \"path\",\n        \"record_id\"\n      ],\n      \"msg\": \"Input should be a valid integer, unable to parse string as an integer\",\n      \"input\": \"SM_008\"\n    }\n  ]\n}"}
2025-08-09 01:13:35,797 - DEBUG - [56e64ff9] HTTP请求开始 - GET None
2025-08-09 01:13:37,869 - WARNING - [56e64ff9] HTTP响应 - GET None - 状态码: 422 - 耗时: 2.072s
2025-08-09 01:13:37,869 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "int_parsing",
      "loc": [
        "path",
        "record_id"
      ],
      "msg": "Input should be a valid integer, unable to parse string as an integer",
      "input": "SM_008"
    }
  ]
}
2025-08-09 01:13:37,870 - DEBUG - HTTP请求详情: {"request_id": "56e64ff9", "timestamp": "2025-08-09T01:13:35.797631", "method": "GET", "url": null, "headers": {"Authorization": "********", "Content-Type": "application/json"}, "status_code": 422, "elapsed_time": "2.072s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"int_parsing\",\n      \"loc\": [\n        \"path\",\n        \"record_id\"\n      ],\n      \"msg\": \"Input should be a valid integer, unable to parse string as an integer\",\n      \"input\": \"SM_008\"\n    }\n  ]\n}"}
2025-08-09 01:13:38,212 - DEBUG - [cc1559a9] HTTP请求开始 - POST None
2025-08-09 01:13:41,050 - INFO - [cc1559a9] HTTP响应 - POST None - 状态码: 200 - 耗时: 2.838s
2025-08-09 01:13:41,051 - DEBUG - HTTP请求详情: {"request_id": "cc1559a9", "timestamp": "2025-08-09T01:13:38.212375", "method": "POST", "url": null, "json": {"username": "admin", "password": "********"}, "headers": {"Content-Type": "application/json"}, "status_code": 200, "elapsed_time": "2.838s", "response": "{\n  \"access_token\": \"********\",\n  \"token_type\": \"******\",\n  \"user\": {\n    \"id\": 1,\n    \"username\": \"admin\",\n    \"email\": \"<EMAIL>\",\n    \"full_name\": \"系统管理员\",\n    \"role\": \"super_admin\",\n    \"is_active\": true,\n    \"custom_id\": \"SM_001\",\n    \"is_first_login\": false\n  },\n  \"custom_id\": \"SM_001\"\n}"}
