#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细测试移动端登录功能的脚本
"""

import sys
import os
import asyncio
import json
import traceback
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.db_connection import get_db_session
from app.api.endpoints.auth import mobile_login
from app.models.user import User
from sqlalchemy.orm import Session

class MockRequest:
    """模拟FastAPI Request对象"""
    def __init__(self, json_data):
        self._json_data = json_data
        self.method = "POST"
        self.url = type('URL', (), {'path': '/api/auth/register/login'})()
        self.client = type('Client', (), {'host': '127.0.0.1'})()
        self.headers = {'user-agent': 'test-client'}
    
    async def json(self):
        return self._json_data

def test_database_connection():
    """测试数据库连接"""
    print("\n=== 测试数据库连接 ===")
    try:
        db = get_db_session()
        print(f"数据库会话类型: {type(db)}")
        
        # 测试查询
        users = db.query(User).all()
        print(f"数据库中用户总数: {len(users)}")
        
        # 查找admin用户
        admin_user = db.query(User).filter(User.username == "admin").first()
        if admin_user:
            print(f"找到admin用户: ID={admin_user.id}, 用户名={admin_user.username}")
            print(f"密码哈希: {admin_user.hashed_password[:50]}...")
            print(f"是否激活: {admin_user.is_active}")
            print(f"角色: {admin_user.role}")
            print(f"创建时间: {admin_user.created_at}")
            if hasattr(admin_user, 'custom_id'):
                print(f"自定义ID: {admin_user.custom_id}")
        else:
            print("未找到admin用户")
        
        db.close()
        return True
    except Exception as e:
        print(f"数据库连接测试失败: {str(e)}")
        traceback.print_exc()
        return False

async def test_mobile_login_function():
    """测试mobile_login函数"""
    print("\n=== 测试mobile_login函数 ===")
    
    try:
        # 创建模拟请求
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        mock_request = MockRequest(login_data)
        
        # 获取数据库会话
        db = get_db_session()
        
        print(f"准备调用mobile_login函数...")
        print(f"请求数据: {login_data}")
        
        # 调用mobile_login函数
        result = await mobile_login(mock_request, db)
        
        print(f"\n登录结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        db.close()
        return result
        
    except Exception as e:
        print(f"测试mobile_login函数时发生错误: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        traceback.print_exc()
        return None

async def test_imports():
    """测试所有必要的导入"""
    print("\n=== 测试导入模块 ===")
    
    try:
        from app.core.security import create_access_token, verify_password
        print("✓ 成功导入 app.core.security")
        
        from app.services.id_generator import IDGenerator
        print("✓ 成功导入 app.services.id_generator")
        
        from app.models.user import User, UserRole
        print("✓ 成功导入 app.models.user")
        
        from app.core.config import settings
        print("✓ 成功导入 app.core.config")
        print(f"访问令牌过期时间: {settings.ACCESS_TOKEN_EXPIRE_MINUTES} 分钟")
        
        return True
        
    except Exception as e:
        print(f"导入模块时发生错误: {str(e)}")
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print(f"开始详细测试 - {datetime.now()}")
    
    # 测试导入
    if not await test_imports():
        print("导入测试失败，停止测试")
        return
    
    # 测试数据库连接
    if not test_database_connection():
        print("数据库连接测试失败，停止测试")
        return
    
    # 测试mobile_login函数
    result = await test_mobile_login_function()
    
    if result:
        if result.get('status') == 'success':
            print("\n✓ 移动端登录测试成功！")
        else:
            print(f"\n✗ 移动端登录测试失败: {result.get('message')}")
    else:
        print("\n✗ 移动端登录测试出现异常")
    
    print(f"\n测试完成 - {datetime.now()}")

if __name__ == "__main__":
    asyncio.run(main())