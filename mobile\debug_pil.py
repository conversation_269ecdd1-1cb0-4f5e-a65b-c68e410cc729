#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PIL调试脚本 - 在应用环境中测试PIL库的状态
"""

import sys
import os
import traceback

def test_pil_basic():
    """测试基本PIL功能"""
    print("=== 测试基本PIL导入 ===")
    try:
        from PIL import Image, ImageDraw
        print(f"PIL导入成功: Image={Image}, ImageDraw={ImageDraw}")
        
        # 测试Image.new
        img = Image.new('RGB', (100, 100), (255, 255, 255))
        print(f"Image.new成功: {img}")
        print(f"图像尺寸: {img.size}")
        
        # 测试ImageDraw.Draw
        draw = ImageDraw.Draw(img)
        print(f"ImageDraw.Draw成功: {draw}")
        
        return True
    except Exception as e:
        print(f"基本PIL测试失败: {e}")
        traceback.print_exc()
        return False

def test_pil_importlib():
    """测试动态导入PIL"""
    print("\n=== 测试动态导入PIL ===")
    try:
        import importlib
        pil_image = importlib.import_module('PIL.Image')
        pil_draw = importlib.import_module('PIL.ImageDraw')
        print(f"动态导入成功: Image={pil_image}, ImageDraw={pil_draw}")
        
        # 测试Image.new
        img = pil_image.new('RGB', (100, 100), (255, 255, 255))
        print(f"pil_image.new成功: {img}")
        
        # 测试ImageDraw.Draw
        draw = pil_draw.Draw(img)
        print(f"pil_draw.Draw成功: {draw}")
        
        return True
    except Exception as e:
        print(f"动态导入PIL测试失败: {e}")
        traceback.print_exc()
        return False

def test_qrcode_basic():
    """测试基本qrcode功能"""
    print("\n=== 测试基本qrcode功能 ===")
    try:
        import qrcode
        qr = qrcode.QRCode(version=1, box_size=10, border=4)
        qr.add_data('test')
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        print(f"qrcode生成成功: {img}")
        print(f"图像类型: {type(img)}")
        
        # 测试转换
        rgba_img = img.convert('RGBA')
        print(f"RGBA转换成功: {rgba_img}")
        
        return True
    except Exception as e:
        print(f"qrcode测试失败: {e}")
        traceback.print_exc()
        return False

def test_pil_in_thread():
    """测试在线程中使用PIL"""
    print("\n=== 测试线程中的PIL ===")
    import threading
    
    def thread_test():
        try:
            from PIL import Image, ImageDraw
            img = Image.new('RGB', (50, 50), (255, 0, 0))
            draw = ImageDraw.Draw(img)
            print(f"线程中PIL测试成功: {img}, {draw}")
            return True
        except Exception as e:
            print(f"线程中PIL测试失败: {e}")
            return False
    
    thread = threading.Thread(target=thread_test)
    thread.start()
    thread.join()

def main():
    print("开始PIL调试测试...")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 运行所有测试
    tests = [
        test_pil_basic,
        test_pil_importlib,
        test_qrcode_basic,
        test_pil_in_thread
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"测试 {test.__name__} 异常: {e}")
            results.append(False)
    
    print("\n=== 测试结果汇总 ===")
    test_names = ['基本PIL', '动态导入PIL', '基本qrcode', '线程PIL']
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "成功" if result else "失败"
        print(f"{name}: {status}")
    
    if all(results):
        print("\n所有测试通过！")
    else:
        print("\n存在测试失败，需要进一步调查。")

if __name__ == '__main__':
    main()