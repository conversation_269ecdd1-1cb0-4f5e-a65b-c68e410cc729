import sqlite3

# 连接数据库
conn = sqlite3.connect(r'C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db')
cursor = conn.cursor()

# 查询所有表
cursor.execute('SELECT name FROM sqlite_master WHERE type="table";')
tables = cursor.fetchall()
print('Tables:', tables)

# 查询medications表结构
try:
    cursor.execute('PRAGMA table_info(medications);')
    columns = cursor.fetchall()
    print('Medications table columns:')
    for col in columns:
        print(f'  {col[1]} {col[2]} (nullable: {not col[3]}, default: {col[4]})')
except Exception as e:
    print(f'Error querying medications table: {e}')

# 查询medication_reminders表结构
try:
    cursor.execute('PRAGMA table_info(medication_reminders);')
    columns = cursor.fetchall()
    print('Medication_reminders table columns:')
    for col in columns:
        print(f'  {col[1]} {col[2]} (nullable: {not col[3]}, default: {col[4]})')
except Exception as e:
    print(f'Error querying medication_reminders table: {e}')

conn.close()