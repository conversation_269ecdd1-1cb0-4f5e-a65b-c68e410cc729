#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终API测试脚本
用于测试修复后的/api/auth/register/login端点
"""

import requests
import json
import sys
import traceback
from datetime import datetime

def test_api_endpoint():
    """
    测试API端点
    """
    print(f"开始测试API端点 - {datetime.now()}")
    print("=" * 50)
    
    # API端点URL
    url = "http://localhost:8006/api/auth/register/login"
    
    # 请求数据
    data = {
        "username": "admin",
        "password": "admin123"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"发送POST请求到: {url}")
        print(f"请求数据: {json.dumps(data, indent=2)}")
        print(f"请求头: {json.dumps(headers, indent=2)}")
        print("-" * 30)
        
        # 发送请求
        response = requests.post(
            url=url,
            json=data,
            headers=headers,
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print("-" * 30)
        
        if response.status_code == 200:
            print("✅ 请求成功!")
            try:
                response_data = response.json()
                print(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                # 检查响应结构
                if 'access_token' in response_data:
                    print("✅ 包含access_token")
                if 'user' in response_data:
                    print("✅ 包含用户信息")
                    user = response_data['user']
                    print(f"   用户名: {user.get('username')}")
                    print(f"   角色: {user.get('role')}")
                    print(f"   Custom ID: {user.get('custom_id')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应内容: {response.text}")
                
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            # 尝试解析错误响应
            try:
                error_data = response.json()
                print(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print("无法解析错误响应为JSON")
                
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
        print("请确保后端服务正在运行")
        
    except requests.exceptions.Timeout as e:
        print(f"❌ 请求超时: {e}")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误堆栈: {traceback.format_exc()}")
        
    print("=" * 50)
    print(f"测试完成 - {datetime.now()}")

def test_health_endpoint():
    """
    测试健康检查端点
    """
    print("\n测试健康检查端点...")
    
    endpoints = [
        "http://localhost:8006/",
        "http://localhost:8006/health",
        "http://localhost:8006/api/health"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=5)
            print(f"✅ {endpoint} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - 错误: {e}")

if __name__ == "__main__":
    # 首先测试健康检查端点
    test_health_endpoint()
    
    # 然后测试登录端点
    test_api_endpoint()