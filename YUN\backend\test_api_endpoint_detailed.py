#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细测试API端点的脚本
"""

import requests
import json
import traceback
from datetime import datetime

def test_api_endpoint():
    """测试API端点"""
    print(f"开始测试API端点 - {datetime.now()}")
    
    base_url = "http://localhost:8006"
    endpoint = "/api/auth/register/login"
    full_url = f"{base_url}{endpoint}"
    
    # 测试数据
    test_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print(f"请求URL: {full_url}")
    print(f"请求数据: {json.dumps(test_data, indent=2)}")
    print(f"请求头: {json.dumps(headers, indent=2)}")
    
    try:
        # 发送POST请求
        response = requests.post(
            full_url,
            json=test_data,
            headers=headers,
            timeout=30
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        # 尝试解析JSON响应
        try:
            response_json = response.json()
            print(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
        except json.JSONDecodeError:
            print(f"响应内容（非JSON）: {response.text}")
        
        # 检查响应状态
        if response.status_code == 200:
            print("\n✓ API端点测试成功！")
        elif response.status_code == 500:
            print("\n✗ 服务器内部错误 (500)")
            print("这通常表示后端代码有异常")
        elif response.status_code == 404:
            print("\n✗ 端点未找到 (404)")
            print("检查路由配置是否正确")
        elif response.status_code == 422:
            print("\n✗ 请求验证失败 (422)")
            print("检查请求数据格式是否正确")
        else:
            print(f"\n? 未预期的状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("\n✗ 连接错误：无法连接到服务器")
        print("请确保后端服务器正在运行")
    except requests.exceptions.Timeout:
        print("\n✗ 请求超时")
    except Exception as e:
        print(f"\n✗ 请求时发生错误: {str(e)}")
        traceback.print_exc()

def test_server_health():
    """测试服务器健康状态"""
    print("\n=== 测试服务器健康状态 ===")
    
    base_url = "http://localhost:8006"
    
    # 测试根路径
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        print(f"根路径状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                print(f"根路径响应: {response.json()}")
            except:
                print(f"根路径响应: {response.text}")
    except Exception as e:
        print(f"根路径测试失败: {str(e)}")
    
    # 测试健康检查
    health_endpoints = ["/health", "/api/health", "/api/v1/health"]
    for endpoint in health_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            print(f"健康检查 {endpoint} 状态码: {response.status_code}")
            if response.status_code == 200:
                try:
                    print(f"  响应: {response.json()}")
                except:
                    print(f"  响应: {response.text[:100]}...")
        except Exception as e:
            print(f"健康检查 {endpoint} 失败: {str(e)}")
    
    # 测试API文档
    try:
        response = requests.get(f"{base_url}/docs", timeout=10)
        print(f"API文档状态码: {response.status_code}")
    except Exception as e:
        print(f"API文档测试失败: {str(e)}")

def main():
    """主函数"""
    test_server_health()
    test_api_endpoint()
    print(f"\n测试完成 - {datetime.now()}")

if __name__ == "__main__":
    main()