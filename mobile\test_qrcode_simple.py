#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的qrcode库测试脚本
用于验证qrcode.make_image方法是否正常工作
"""

import qrcode
from PIL import Image

def test_basic_qrcode():
    """测试基本的二维码生成"""
    print("=== 基本二维码生成测试 ===")
    
    # 创建QR码对象
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_H,
        box_size=10,
        border=4
    )
    
    # 添加数据
    test_data = "Hello World Test"
    print(f"添加数据: {test_data}")
    qr.add_data(test_data)
    qr.make(fit=True)
    
    print(f"QR版本: {qr.version}")
    print(f"模块数量: {getattr(qr, 'modules_count', 'N/A')}")
    
    # 测试1: 默认make_image
    print("\n--- 测试1: 默认make_image ---")
    try:
        img1 = qr.make_image()
        print(f"默认make_image结果: {img1}, 类型: {type(img1)}")
        if img1:
            print(f"图像尺寸: {img1.size}, 模式: {img1.mode}")
    except Exception as e:
        print(f"默认make_image失败: {e}")
    
    # 测试2: 带颜色参数的make_image
    print("\n--- 测试2: 带颜色参数的make_image ---")
    try:
        img2 = qr.make_image(fill_color="black", back_color="white")
        print(f"带颜色make_image结果: {img2}, 类型: {type(img2)}")
        if img2:
            print(f"图像尺寸: {img2.size}, 模式: {img2.mode}")
    except Exception as e:
        print(f"带颜色make_image失败: {e}")
    
    # 测试3: 使用PIL图像工厂
    print("\n--- 测试3: 使用PIL图像工厂 ---")
    try:
        from qrcode.image.pil import PilImage
        img3 = qr.make_image(image_factory=PilImage, fill_color="#20b840", back_color="white")
        print(f"PIL工厂make_image结果: {img3}, 类型: {type(img3)}")
        if img3:
            print(f"图像尺寸: {img3.size}, 模式: {img3.mode}")
    except Exception as e:
        print(f"PIL工厂make_image失败: {e}")
    
    # 测试4: 检查qrcode模块的内部状态
    print("\n--- 测试4: 检查qrcode模块状态 ---")
    try:
        print(f"qrcode.constants: {dir(qrcode.constants)}")
        print(f"qrcode.image: {hasattr(qrcode, 'image')}")
        if hasattr(qrcode, 'image'):
            print(f"qrcode.image.pil: {hasattr(qrcode.image, 'pil')}")
    except Exception as e:
        print(f"检查qrcode模块状态失败: {e}")

def test_complex_qrcode():
    """测试复杂数据的二维码生成"""
    print("\n=== 复杂数据二维码生成测试 ===")
    
    # 创建QR码对象 - 使用与应用相同的配置
    qr = qrcode.QRCode(
        version=3,
        error_correction=qrcode.constants.ERROR_CORRECT_H,
        box_size=10,
        border=4
    )
    
    # 使用与应用相同的复杂数据
    complex_data = 'HEALTH_TREA:{"type": "health_data", "data": {"user_id": "SM_008", "timestamp": "2025-01-01T12:00:00"}, "expiry": **********}'
    print(f"添加复杂数据: {complex_data[:50]}...")
    qr.add_data(complex_data)
    qr.make(fit=True)
    
    print(f"QR版本: {qr.version}")
    print(f"模块数量: {getattr(qr, 'modules_count', 'N/A')}")
    
    # 测试make_image
    try:
        img = qr.make_image(fill_color="#20b840", back_color="white")
        print(f"复杂数据make_image结果: {img}, 类型: {type(img)}")
        if img:
            print(f"图像尺寸: {img.size}, 模式: {img.mode}")
            # 尝试保存图像
            img.save("test_complex_qr.png")
            print("复杂数据二维码保存成功: test_complex_qr.png")
    except Exception as e:
        print(f"复杂数据make_image失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始qrcode库测试...")
    print(f"qrcode模块位置: {qrcode.__file__}")
    print(f"PIL版本: {Image.__version__ if hasattr(Image, '__version__') else 'Unknown'}")
    
    test_basic_qrcode()
    test_complex_qrcode()
    
    print("\n测试完成!")