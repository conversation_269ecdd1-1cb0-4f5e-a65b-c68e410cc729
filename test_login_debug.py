#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录功能调试脚本
直接测试mobile_login函数并捕获详细错误信息
"""

import sys
import os
import asyncio
import json
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

from app.api.endpoints.auth import mobile_login
from app.core.db_connection import get_db_session
from app.schemas.user import Login
from fastapi import HTTPException
import traceback

class MockRequest:
    """模拟FastAPI的Request对象"""
    def __init__(self, json_data):
        self._json_data = json_data
    
    async def json(self):
        return self._json_data

async def test_mobile_login_with_exception_handling():
    """
    测试mobile_login函数并捕获详细异常信息
    """
    try:
        print("开始测试mobile_login函数...")
        
        # 创建模拟请求
        request_data = {"username": "admin", "password": "admin123"}
        mock_request = MockRequest(request_data)
        print(f"创建模拟请求: {request_data['username']}")
        
        # 获取数据库会话
        print("获取数据库会话...")
        db = get_db_session()
        print("数据库会话获取成功")
        
        # 调用mobile_login函数
        print("调用mobile_login函数...")
        result = await mobile_login(mock_request, db)
        print(f"登录结果: {result}")
        
        # 关闭数据库会话
        db.close()
        print("数据库会话已关闭")
        
        return result
        
    except HTTPException as e:
        print(f"HTTPException: {e.status_code} - {e.detail}")
        traceback.print_exc()
        return None
    except Exception as e:
        print(f"Exception: {type(e).__name__}: {str(e)}")
        traceback.print_exc()
        return None
    finally:
        try:
            if 'db' in locals():
                db.close()
        except:
            pass

if __name__ == "__main__":
    print("=== 移动端登录功能调试测试 ===")
    result = asyncio.run(test_mobile_login_with_exception_handling())
    
    if result:
        print("\n✅ 测试成功!")
        print(f"结果: {result}")
    else:
        print("\n❌ 测试失败!")
    
    print("\n调试测试完成!")