#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的认证端点调试脚本
用于调试认证端点返回500错误的问题
"""

import requests
import json
import time

def test_auth_endpoints_detailed():
    """详细测试认证端点"""
    base_url = "http://localhost:8006"
    
    print("=== 详细认证端点调试 ===")
    
    # 测试数据
    test_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    # 测试端点列表
    endpoints = [
        {
            "path": "/api/auth/register/login",
            "method": "POST",
            "data": test_data,
            "content_type": "application/json"
        },
        {
            "path": "/api/auth/login",
            "method": "POST",
            "data": test_data,
            "content_type": "application/json"
        },
        {
            "path": "/api/auth/simple_login",
            "method": "POST",
            "data": test_data,
            "content_type": "application/json"
        },
        {
            "path": "/auth/login",
            "method": "POST",
            "data": test_data,
            "content_type": "application/json"
        },
        {
            "path": "/api/auth/login",
            "method": "POST",
            "data": test_data,
            "content_type": "application/x-www-form-urlencoded"
        }
    ]
    
    for endpoint in endpoints:
        print(f"\n--- 测试端点: {endpoint['path']} ---")
        print(f"方法: {endpoint['method']}")
        print(f"内容类型: {endpoint['content_type']}")
        print(f"数据: {endpoint['data']}")
        
        try:
            url = base_url + endpoint['path']
            
            # 设置请求头
            headers = {
                "Content-Type": endpoint['content_type'],
                "User-Agent": "DebugScript/1.0",
                "Accept": "application/json"
            }
            
            # 发送请求
            if endpoint['content_type'] == "application/json":
                response = requests.post(
                    url,
                    json=endpoint['data'],
                    headers=headers,
                    timeout=10
                )
            else:
                response = requests.post(
                    url,
                    data=endpoint['data'],
                    headers=headers,
                    timeout=10
                )
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            # 尝试解析响应内容
            try:
                if response.headers.get('content-type', '').startswith('application/json'):
                    response_data = response.json()
                    print(f"响应内容 (JSON): {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                else:
                    response_text = response.text
                    print(f"响应内容 (文本): {response_text[:500]}..." if len(response_text) > 500 else f"响应内容 (文本): {response_text}")
            except Exception as e:
                print(f"解析响应内容失败: {str(e)}")
                print(f"原始响应: {response.text[:200]}..." if len(response.text) > 200 else f"原始响应: {response.text}")
            
            # 检查是否有错误详情
            if response.status_code >= 400:
                print(f"❌ 请求失败 - 状态码: {response.status_code}")
                if hasattr(response, 'reason'):
                    print(f"错误原因: {response.reason}")
            else:
                print(f"✅ 请求成功 - 状态码: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
        except requests.exceptions.ConnectionError:
            print("❌ 连接错误 - 服务器可能未启动")
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
        
        # 等待一下，避免请求过快
        time.sleep(0.5)
    
    print("\n=== 测试健康检查端点 ===")
    health_endpoints = ["/health", "/api/health", "/api/health/ping"]
    
    for endpoint in health_endpoints:
        try:
            url = base_url + endpoint
            response = requests.get(url, timeout=5)
            print(f"{endpoint}: {response.status_code}")
            if response.status_code == 200:
                try:
                    print(f"  响应: {response.json()}")
                except:
                    print(f"  响应: {response.text[:100]}")
        except Exception as e:
            print(f"{endpoint}: 请求失败 - {str(e)}")

if __name__ == "__main__":
    test_auth_endpoints_detailed()