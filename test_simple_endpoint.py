#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简单的API端点
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
import uvicorn
import asyncio

app = FastAPI()

@app.post("/test/login")
async def test_login(request: Request):
    """
    测试登录端点
    """
    try:
        # 解析请求体
        request_data = await request.json()
        username = request_data.get('username')
        password = request_data.get('password')
        
        print(f"收到登录请求: username={username}, password={password}")
        
        if username == "admin" and password == "admin123":
            return {
                "status": "success",
                "message": "登录成功",
                "user": {
                    "username": username,
                    "role": "admin"
                }
            }
        else:
            return {
                "status": "error",
                "message": "用户名或密码错误"
            }
            
    except Exception as e:
        print(f"登录端点异常: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"服务器错误: {str(e)}"}
        )

if __name__ == "__main__":
    print("启动测试服务器...")
    uvicorn.run(app, host="0.0.0.0", port=8007)