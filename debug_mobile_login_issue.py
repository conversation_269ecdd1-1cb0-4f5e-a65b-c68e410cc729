#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端登录问题诊断脚本
用于测试移动端登录API的连接性和功能性
"""

import requests
import json
import sys
import os
from urllib.parse import urljoin

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'mobile'))

def test_server_connectivity():
    """测试服务器连接性"""
    print("=== 测试服务器连接性 ===")
    
    servers = [
        "http://localhost:8006",
        "http://127.0.0.1:8006",
        "http://************"
    ]
    
    for server in servers:
        try:
            print(f"\n测试服务器: {server}")
            
            # 测试健康检查端点
            health_url = f"{server}/api/health/check"
            response = requests.get(health_url, timeout=5)
            print(f"健康检查状态: {response.status_code}")
            if response.status_code == 200:
                print(f"健康检查响应: {response.json()}")
            
        except requests.exceptions.ConnectionError:
            print(f"连接失败: {server} - 连接被拒绝")
        except requests.exceptions.Timeout:
            print(f"连接超时: {server}")
        except Exception as e:
            print(f"连接错误: {server} - {str(e)}")

def test_login_endpoints():
    """测试不同的登录端点"""
    print("\n=== 测试登录端点 ===")
    
    base_url = "http://localhost:8006"
    
    # 测试数据
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    endpoints = [
        "/api/auth/register/login",  # 移动端专用登录端点
        "/api/auth/login",           # 标准登录端点
        "/api/mobile/login",         # 移动端API登录端点
        "/api/mobile/login_json"     # 移动端JSON登录端点
    ]
    
    for endpoint in endpoints:
        try:
            print(f"\n测试端点: {endpoint}")
            url = f"{base_url}{endpoint}"
            
            # 尝试JSON格式请求
            headers = {'Content-Type': 'application/json'}
            response = requests.post(url, json=login_data, headers=headers, timeout=10)
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            try:
                response_data = response.json()
                print(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应文本: {response.text[:500]}")
                
        except Exception as e:
            print(f"请求失败: {str(e)}")

def test_mobile_cloud_api():
    """测试移动端CloudAPI类的登录功能"""
    print("\n=== 测试移动端CloudAPI登录 ===")
    
    try:
        # 导入移动端CloudAPI
        from mobile.utils.cloud_api import CloudAPI
        
        # 创建CloudAPI实例
        api = CloudAPI()
        print(f"CloudAPI实例创建成功")
        print(f"当前服务器: {api.base_url}")
        
        # 测试认证
        result = api.authenticate(username="admin", password="admin123")
        print(f"认证结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result and result.get('status') == 'success':
            print("移动端登录成功！")
            return True
        else:
            print("移动端登录失败！")
            return False
            
    except ImportError as e:
        print(f"导入CloudAPI失败: {str(e)}")
        return False
    except Exception as e:
        print(f"CloudAPI测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_url_building():
    """测试URL构建逻辑"""
    print("\n=== 测试URL构建逻辑 ===")
    
    try:
        from mobile.utils.cloud_api_helpers import RequestHelper
        
        base_urls = [
            "http://localhost:8006",
            "http://localhost:8006/",
            "http://localhost:8006/api",
            "http://localhost:8006/api/"
        ]
        
        endpoint = "auth/register/login"
        
        for base_url in base_urls:
            built_url = RequestHelper.build_url(base_url, endpoint)
            print(f"基础URL: {base_url} -> 构建URL: {built_url}")
            
    except Exception as e:
        print(f"URL构建测试失败: {str(e)}")

def main():
    """主函数"""
    print("移动端登录问题诊断脚本")
    print("=" * 50)
    
    # 1. 测试服务器连接性
    test_server_connectivity()
    
    # 2. 测试URL构建逻辑
    test_url_building()
    
    # 3. 测试登录端点
    test_login_endpoints()
    
    # 4. 测试移动端CloudAPI
    mobile_login_success = test_mobile_cloud_api()
    
    print("\n=== 诊断总结 ===")
    if mobile_login_success:
        print("✅ 移动端登录功能正常")
    else:
        print("❌ 移动端登录存在问题，请检查上述输出")
    
    print("\n诊断完成！")

if __name__ == "__main__":
    main()