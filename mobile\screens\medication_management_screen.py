#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
medication_management_screen.py（完整重构版）
- KivyMD 2.0.1.dev0 兼容
- Android (APK) 优先，支持 AlarmManager + PendingIntent（示例）
- 卡片右侧包含操作按钮：编辑 / 提醒 / 停药 / 删除
- 支持云同步（上传）和云导入（下载）
- 使用 theme.py 中的 AppTheme / AppMetrics / FontStyles
- 所有注释中文
"""

import os
import json
import logging
import sqlite3
import hashlib
import traceback
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional

# Kivy / KivyMD
from kivy.metrics import dp, sp
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.factory import Factory
from kivy.utils import platform
from kivy.app import App

from kivy.properties import StringProperty, ListProperty, ObjectProperty, BooleanProperty, NumericProperty
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.widget import Widget

from kivymd.app import MDApp
from kivymd.uix.card import MDCard
from screens.base_screen import BaseScreen
from kivymd.uix.label import MDLabel
from widgets.logo import HealthLogo
from kivymd.uix.button import MDIconButton, MDButton, MDButtonText
from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText
from kivymd.uix.textfield import MDTextFieldHelperText
# 统一 toast 封装，兼容 Windows/非Android 环境 - 修复 NoneType 错误
def toast(message):
    """安全的toast函数，防止NoneType错误"""
    try:
        # 确保message不为None且为字符串
        if message is None:
            message = "未知消息"
        message_str = str(message).replace('\n', ' ').replace('\r', ' ') if hasattr(message, 'replace') else str(message)

        try:
            from kivymd.toast import toast as _md_toast
            _md_toast(message_str)
        except Exception:
            try:
                from plyer import notification as _plyer_notification
                _plyer_notification.notify(title="提示", message=message_str)
            except Exception:
                try:
                    from kivymd.uix.snackbar import Snackbar
                    Snackbar(text=message_str).open()
                except Exception:
                    print(f"[Toast] {message_str}")
    except Exception as e:
        print(f"[Toast Error] {e}: {message}")

# 导入你提供的 theme.py（请确保文件存在）
try:
    from theme import AppTheme, AppMetrics, FontStyles
except Exception:
    # 如果 theme.py 不可用，提供简单降级默认值以避免运行时崩溃
    class _DummyTheme:
        PRIMARY_COLOR = (0.13, 0.59, 0.95, 1)
        PRIMARY_LIGHT = (0.9, 0.95, 1, 1)
        SURFACE_COLOR = (1, 1, 1, 1)
        TEXT_LIGHT = (1, 1, 1, 1)
        CARD_BACKGROUND = (1, 1, 1, 1)
        DIMENSIONS = {'text_field_height': dp(40), 'text_field_multiline_height': dp(64), 'button_width_small': dp(80)}
    AppTheme = _DummyTheme()
    AppMetrics = type('AM', (), {'CARD_RADIUS': dp(8)})
    FontStyles = type('FS', (), {'TITLE': 'H6', 'BODY': 'Body1'})

# 平台与通知能力检测
IS_ANDROID = platform == 'android'
try:
    if IS_ANDROID:
        # pyjnius 用于调用 Android 原生 API
        from jnius import autoclass, cast
        # 标记通知可用（但初始化可能在 NotificationManager 中检测失败）
        _HAS_PYJNIUS = True
    else:
        _HAS_PYJNIUS = False
except Exception:
    _HAS_PYJNIUS = False

try:
    if not IS_ANDROID:
        from plyer import notification as plyer_notification
        _HAS_PLYER = True
    else:
        _HAS_PLYER = False
except Exception:
    _HAS_PLYER = False

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 数据库表名
MEDICATIONS_TABLE = "medications"
REMINDERS_TABLE = "medication_reminders"

# -------------------------
# 数据库管理器
# -------------------------
class MedicationDatabaseManager:
    """
    用药数据库管理器（SQLite）
    - 含药物表与提醒表
    - 提供增删改查、提醒保存、导出等功能
    """

    def __init__(self, db_path: Optional[str] = None):
        # 使用指定的数据库路径
        if db_path is None:
            # 使用后端数据库路径
            db_path = r"C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db"
        self.db_path = str(db_path)
        self._init_db()

    def _connect(self):
        return sqlite3.connect(self.db_path)

    def _init_db(self):
        """初始化表结构与索引 - 适配后端数据库结构"""
        try:
            with self._connect() as conn:
                c = conn.cursor()
                # 检查medications表是否存在必要字段，如果不存在则添加
                try:
                    # 尝试添加status字段
                    c.execute(f"ALTER TABLE {MEDICATIONS_TABLE} ADD COLUMN status TEXT DEFAULT 'active'")
                    logger.info("[MedicationDB] 添加status字段")
                except sqlite3.OperationalError:
                    pass  # 字段已存在
                
                try:
                    # 尝试添加stop_reason字段
                    c.execute(f"ALTER TABLE {MEDICATIONS_TABLE} ADD COLUMN stop_reason TEXT")
                    logger.info("[MedicationDB] 添加stop_reason字段")
                except sqlite3.OperationalError:
                    pass  # 字段已存在
                
                try:
                    # 尝试添加stop_date字段
                    c.execute(f"ALTER TABLE {MEDICATIONS_TABLE} ADD COLUMN stop_date DATETIME")
                    logger.info("[MedicationDB] 添加stop_date字段")
                except sqlite3.OperationalError:
                    pass  # 字段已存在
                
                try:
                    # 尝试添加reason字段（用药原因）
                    c.execute(f"ALTER TABLE {MEDICATIONS_TABLE} ADD COLUMN reason TEXT")
                    logger.info("[MedicationDB] 添加reason字段")
                except sqlite3.OperationalError:
                    pass  # 字段已存在
                
                # 添加提醒相关字段到medications表
                try:
                    # 用药提醒时间（JSON格式存储多个时间）
                    c.execute(f"ALTER TABLE {MEDICATIONS_TABLE} ADD COLUMN reminder_times TEXT")
                    logger.info("[MedicationDB] 添加reminder_times字段")
                except sqlite3.OperationalError:
                    pass  # 字段已存在
                
                try:
                    # 复查提醒设置（JSON格式）
                    c.execute(f"ALTER TABLE {MEDICATIONS_TABLE} ADD COLUMN review_reminder TEXT")
                    logger.info("[MedicationDB] 添加review_reminder字段")
                except sqlite3.OperationalError:
                    pass  # 字段已存在
                
                try:
                    # 提醒是否启用
                    c.execute(f"ALTER TABLE {MEDICATIONS_TABLE} ADD COLUMN reminder_enabled INTEGER DEFAULT 0")
                    logger.info("[MedicationDB] 添加reminder_enabled字段")
                except sqlite3.OperationalError:
                    pass  # 字段已存在
                
                # 创建提醒表（如果不存在）
                c.execute(f"""CREATE TABLE IF NOT EXISTS {REMINDERS_TABLE}_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    medication_id INTEGER NOT NULL,
                    custom_id TEXT NOT NULL,
                    reminder_time TEXT NOT NULL,
                    reminder_type TEXT DEFAULT 'daily',
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT,
                    FOREIGN KEY (medication_id) REFERENCES {MEDICATIONS_TABLE} (id)
                )""")
                
                # 创建索引
                c.execute(f"CREATE INDEX IF NOT EXISTS idx_med_custom ON {MEDICATIONS_TABLE} (custom_id)")
                c.execute(f"CREATE INDEX IF NOT EXISTS idx_med_status ON {MEDICATIONS_TABLE} (status)")
                c.execute(f"CREATE INDEX IF NOT EXISTS idx_rem_custom ON {REMINDERS_TABLE}_new (custom_id)")
                conn.commit()
            logger.info("[MedicationDB] 初始化完成，路径：%s", self.db_path)
        except Exception as e:
            logger.error("[MedicationDB] 初始化失败: %s", e)
            raise

    def _now(self):
        return datetime.now().isoformat()

    def encrypt_sensitive_data(self, data: str) -> str:
        """示例性哈希加密，生产环境请使用可逆加密或更安全方案"""
        try:
            return hashlib.sha256((data or "").encode()).hexdigest()
        except Exception:
            return data or ""

    def save_medication(self, custom_id: str, medication: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """插入用药记录，返回插入记录（包含 reminders）- 适配后端数据库结构"""
        try:
            now = self._now()
            with self._connect() as conn:
                c = conn.cursor()
                
                # 检查是否存在重复药物（相同名称和剂量的当前用药）
                c.execute(f"""
                    SELECT COUNT(*) FROM {MEDICATIONS_TABLE} 
                    WHERE custom_id = ? AND name = ? AND dosage = ? AND status = 'active'
                """, (custom_id, medication.get('name', ''), medication.get('dosage', '')))
                
                if c.fetchone()[0] > 0:
                    error_msg = f"药物 '{medication.get('name', '')} {medication.get('dosage', '')}' 已存在，请勿重复添加"
                    logger.warning(f"[MedicationDB] {error_msg}")
                    raise ValueError(error_msg)
                
                # 准备提醒数据
                reminder_times = None
                review_reminder = None
                reminder_enabled = 0
                
                if 'reminder_data' in medication:
                    reminder_data = medication['reminder_data']
                    if reminder_data.get('times'):
                        reminder_times = json.dumps(reminder_data['times'], ensure_ascii=False)
                        reminder_enabled = 1
                    if reminder_data.get('review_weeks'):
                        review_reminder = json.dumps({
                            'weeks': reminder_data['review_weeks'],
                            'enabled': True
                        }, ensure_ascii=False)
                
                # 适配后端数据库结构，将reason映射到instructions字段，并添加提醒字段
                c.execute(f"""INSERT INTO {MEDICATIONS_TABLE}
                    (custom_id, name, dosage, frequency, start_date, end_date, instructions, notes, status, reminder_times, review_reminder, reminder_enabled, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                          (
                              custom_id,
                              medication.get('name', ''),
                              medication.get('dosage', ''),
                              medication.get('frequency', ''),
                              medication.get('start_date', ''),
                              medication.get('end_date'),
                              medication.get('reason', ''),  # reason映射到instructions
                              medication.get('notes', ''),
                              medication.get('status', 'active'),
                              reminder_times,
                              review_reminder,
                              reminder_enabled,
                              now, now
                          ))
                mid = c.lastrowid
                conn.commit()
            return self.get_medication_by_id(mid)
        except ValueError as ve:
            # 重复药物错误，直接抛出给上层处理
            raise ve
        except Exception as e:
            logger.error("[MedicationDB] save_medication 失败: %s", e)
            return None

    def update_medication(self, med_id: int, updates: Dict[str, Any]) -> bool:
        """更新药物记录（部分字段）- 适配后端数据库结构"""
        try:
            # 适配后端数据库结构，将reason字段映射到instructions
            if 'reason' in updates:
                updates['instructions'] = updates.pop('reason')
            
            set_clause = ", ".join([f"{k} = ?" for k in updates.keys()])
            params = list(updates.values())
            params.append(med_id)
            now = self._now()
            with self._connect() as conn:
                c = conn.cursor()
                c.execute(f"UPDATE {MEDICATIONS_TABLE} SET {set_clause}, updated_at = ? WHERE id = ?",
                          (*params[:-1], now, params[-1]))
                conn.commit()
                return c.rowcount > 0
        except Exception as e:
            logger.error("[MedicationDB] update_medication 失败: %s", e)
            return False

    def update_medication_status(self, med_id: int, status: str, stop_date: Optional[str] = None, stop_reason: Optional[str] = None) -> bool:
        """更新状态：例如停用（stopped）"""
        try:
            now = self._now()
            with self._connect() as conn:
                c = conn.cursor()
                if status == 'stopped':
                    if not stop_date:
                        stop_date = datetime.now().strftime("%Y-%m-%d")
                    c.execute(f"UPDATE {MEDICATIONS_TABLE} SET status = ?, stop_date = ?, stop_reason = ?, updated_at = ? WHERE id = ?",
                              (status, stop_date, stop_reason, now, med_id))
                else:
                    c.execute(f"UPDATE {MEDICATIONS_TABLE} SET status = ?, updated_at = ? WHERE id = ?", (status, now, med_id))
                conn.commit()
                return c.rowcount > 0
        except Exception as e:
            logger.error("[MedicationDB] update_medication_status 失败: %s", e)
            return False

    def delete_medication(self, med_id: int) -> bool:
        """删除药物记录"""
        try:
            with self._connect() as conn:
                c = conn.cursor()
                c.execute(f"DELETE FROM {MEDICATIONS_TABLE} WHERE id = ?", (med_id,))
                conn.commit()
                return c.rowcount > 0
        except Exception as e:
            logger.error("[MedicationDB] delete_medication 失败: %s", e)
            return False

    def get_medication_by_id(self, med_id: int) -> Optional[Dict[str, Any]]:
        """根据 ID 获取药物，并附上 reminder_settings - 适配后端数据库结构"""
        try:
            with self._connect() as conn:
                c = conn.cursor()
                c.execute(f"SELECT * FROM {MEDICATIONS_TABLE} WHERE id = ?", (med_id,))
                row = c.fetchone()
                if not row:
                    return None
                cols = [d[0] for d in c.description]
                med = dict(zip(cols, row))
                # 将后端数据库的instructions字段映射回reason字段
                if 'instructions' in med and 'reason' not in med:
                    med['reason'] = med.get('instructions', '')
                med['reminder_settings'] = self._get_medication_reminders(med_id)
                return med
        except Exception as e:
            logger.error("[MedicationDB] get_medication_by_id 失败: %s", e)
            return None

    def get_medications(self, custom_id: str, status: str = 'active') -> List[Dict[str, Any]]:
        """获取某用户某状态下的用药列表，附带 reminder_settings - 适配后端数据库结构"""
        try:
            with self._connect() as conn:
                c = conn.cursor()
                c.execute(f"SELECT * FROM {MEDICATIONS_TABLE} WHERE custom_id = ? AND status = ? ORDER BY created_at DESC", (custom_id, status))
                rows = c.fetchall()
                cols = [d[0] for d in c.description]
                meds = [dict(zip(cols, r)) for r in rows]
                for m in meds:
                    # 将后端数据库的instructions字段映射回reason字段
                    if 'instructions' in m and 'reason' not in m:
                        m['reason'] = m.get('instructions', '')
                    m['reminder_settings'] = self._get_medication_reminders(m.get('id'))
                return meds
        except Exception as e:
            logger.error("[MedicationDB] get_medications 失败: %s", e)
            return []

    def _get_medication_reminders(self, medication_id: int) -> Dict[str, Any]:
        """从medications表中解析提醒设置"""
        try:
            if not medication_id:
                return {}
            with self._connect() as conn:
                c = conn.cursor()
                c.execute("SELECT reminder_times, review_reminder, reminder_enabled FROM medications WHERE id = ?", (medication_id,))
                row = c.fetchone()
                if not row:
                    return {}
                
                reminder_times, review_reminder, reminder_enabled = row
                settings = {'med_reminder': {'enabled': False, 'times': []}, 'review_reminder': {'enabled': False}}
                
                # 解析日常提醒时间
                if reminder_times and reminder_enabled:
                    try:
                        if isinstance(reminder_times, str):
                            times_data = json.loads(reminder_times)
                            if isinstance(times_data, list):
                                settings['med_reminder']['enabled'] = True
                                settings['med_reminder']['times'] = times_data
                    except Exception as e:
                        logger.error("[MedicationDB] 解析 reminder_times 失败: %s", e)
                
                # 解析复查提醒
                if review_reminder:
                    try:
                        if isinstance(review_reminder, str):
                            review_data = json.loads(review_reminder)
                            if review_data.get('enabled'):
                                settings['review_reminder']['enabled'] = True
                                settings['review_reminder']['date'] = review_data.get('date', '')
                                settings['review_reminder']['advance_days'] = review_data.get('advance_days', '3')
                    except Exception as e:
                        logger.error("[MedicationDB] 解析 review_reminder 失败: %s", e)
                
                return settings
        except Exception as e:
            logger.error("[MedicationDB] _get_medication_reminders 失败: %s", e)
            return {}

    def save_reminder(self, medication_id: int, custom_id: str, reminder_data: Dict[str, Any]) -> bool:
        """保存提醒设置到medications表"""
        try:
            now = self._now()
            reminder_type = reminder_data.get('reminder_type', 'daily')
            
            with self._connect() as conn:
                c = conn.cursor()
                
                if reminder_type in ('medication', 'daily'):
                    # 更新日常提醒时间
                    reminder_times = reminder_data.get('reminder_times', [])
                    reminder_times_json = json.dumps(reminder_times) if reminder_times else None
                    c.execute("UPDATE medications SET reminder_times = ?, reminder_enabled = ?, updated_at = ? WHERE id = ? AND custom_id = ?",
                             (reminder_times_json, 1 if reminder_times else 0, now, medication_id, custom_id))
                elif reminder_type == 'review':
                    # 更新复查提醒
                    review_data = {
                        'enabled': True,
                        'date': reminder_data.get('date', ''),
                        'advance_days': reminder_data.get('advance_days', '3')
                    }
                    review_json = json.dumps(review_data)
                    c.execute("UPDATE medications SET review_reminder = ?, updated_at = ? WHERE id = ? AND custom_id = ?",
                             (review_json, now, medication_id, custom_id))
                
                conn.commit()
                return True
        except Exception as e:
            logger.error("[MedicationDB] save_reminder 失败: %s", e)
            return False

    def export_data(self, custom_id: str, export_path: str) -> bool:
        """导出当前用户数据为 JSON"""
        try:
            current = self.get_medications(custom_id, status='active')
            history = self.get_medications(custom_id, status='stopped')
            data = {
                'export_time': self._now(),
                'custom_id': custom_id,
                'current_medications': current,
                'history_medications': history,
                'total_count': len(current) + len(history)
            }
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error("[MedicationDB] export_data 失败: %s", e)
            return False

# 单例获取数据库管理器
_db_manager: Optional[MedicationDatabaseManager] = None
def get_medication_db_manager() -> MedicationDatabaseManager:
    global _db_manager
    if _db_manager is None:
        _db_manager = MedicationDatabaseManager()
    return _db_manager

# -------------------------
# 通知管理器（Android: AlarmManager + PendingIntent；其他: plyer）
# -------------------------
class NotificationManager:
    """
    通知管理器
    - Android: 使用 AlarmManager.setExactAndAllowWhileIdle 进行单次提醒（示例）
      注意：为可靠性与长期/重复任务，生产应实现 Java 层 BroadcastReceiver 或使用 WorkManager
    - 非 Android: 使用 plyer.notification 立即发送（演示性）
    """

    def __init__(self):
        self.is_android = IS_ANDROID and _HAS_PYJNIUS
        self.available = False
        if self.is_android:
            try:
                # Android Java 类引用
                self.PythonActivity = autoclass('org.kivy.android.PythonActivity')
                self.activity = self.PythonActivity.mActivity
                self.Context = autoclass('android.content.Context')
                self.Intent = autoclass('android.content.Intent')
                self.PendingIntent = autoclass('android.app.PendingIntent')
                self.AlarmManager = autoclass('android.app.AlarmManager')
                self.System = autoclass('java.lang.System')
                logger.info("[Notification] Android 原生 API 就绪")
                self.available = True
            except Exception as e:
                logger.error("[Notification] 初始化 Android 通知失败: %s", e)
                self.available = False
        else:
            if _HAS_PLYER:
                self.plyer = plyer_notification
                self.available = True
            else:
                self.plyer = None
                self.available = False

    def send_immediate(self, title: str, message: str) -> bool:
        """立即发送通知（跨平台回退）"""
        try:
            if self.is_android:
                # 在 Android 上更稳妥的做法是使用 Java NotificationManager 创建 NotificationChannel 并发通知
                # 这里采用 plyer.notify 作为演示（取决于 plyer 后端）
                try:
                    from plyer import notification as plyer_notification
                    plyer_notification.notify(title=title, message=message)
                    return True
                except Exception as e:
                    logger.error("[Notification] Android send_immediate 使用 plyer 失败: %s", e)
                    return False
            else:
                if self.plyer:
                    self.plyer.notify(title=title, message=message)
                    return True
            return False
        except Exception as e:
            logger.error("[Notification] send_immediate 异常: %s", e)
            return False

    def schedule_medication_reminder(self, med_id: int, med_name: str, notify_dt: datetime) -> bool:
        """
        在指定时间安排一次提醒
        Android: 使用 AlarmManager.setExactAndAllowWhileIdle（API 23+）
        注意：
         - 生产中建议配合 Java 层 BroadcastReceiver 来显示通知
         - Android 12+ 对 exact alarms 可能需要用户允许或声明特殊权限
        """
        if not self.available:
            logger.warning("[Notification] 通知不可用")
            return False
        try:
            if self.is_android:
                # 将通知时间转换为毫秒 epoch（UTC）
                target_ms = int(notify_dt.timestamp() * 1000)
                am = cast('android.app.AlarmManager', self.activity.getSystemService(self.Context.ALARM_SERVICE))

                # 构建 Intent（示例：启动 PythonActivity；在实际应用中建议使用 BroadcastReceiver）
                action = f"org.yourapp.medication_reminder.{med_id}"
                intent = self.Intent(self.activity.getApplicationContext(), self.PythonActivity)
                # 也可以用 Intent(action) 并配合 BroadcastReceiver 注册
                intent.setAction(action)
                intent.putExtra("med_id", int(med_id))
                intent.putExtra("med_name", str(med_name))
                intent.putExtra("notify_time", notify_dt.isoformat())

                # requestCode 建议使用 med_id（确保为 int）
                request_code = int(med_id) if med_id else abs(hash(med_name)) & 0x7fffffff

                # 创建 PendingIntent - 这里使用 getActivity 作为示例
                FLAG_UPDATE_CURRENT = self.PendingIntent.FLAG_UPDATE_CURRENT
                pi = self.PendingIntent.getActivity(self.activity.getApplicationContext(), request_code, intent, FLAG_UPDATE_CURRENT)

                # 首选 setExactAndAllowWhileIdle；若不可用回退到 set
                try:
                    am.setExactAndAllowWhileIdle(self.AlarmManager.RTC_WAKEUP, target_ms, pi)
                except Exception:
                    am.set(self.AlarmManager.RTC_WAKEUP, target_ms, pi)

                logger.info("[Notification] 安排 Android 提醒: %s @ %s", med_name, notify_dt.isoformat())
                return True
            else:
                # 非 Android 平台：无法长期可靠定时，示例使用 plyer 立即发送或简单延时处理（不可靠）
                if self.plyer:
                    # 若 notify_dt 在未来，仅提示演示性消息
                    self.plyer.notify(title=f"安排提醒：{med_name}", message=f"计划时间：{notify_dt.strftime('%Y-%m-%d %H:%M')}")
                    return True
            return False
        except Exception as e:
            logger.error("[Notification] schedule_medication_reminder 异常: %s", e)
            return False

# 单例
_notification_manager: Optional[NotificationManager] = None
def get_notification_manager() -> NotificationManager:
    global _notification_manager
    if _notification_manager is None:
        _notification_manager = NotificationManager()
    return _notification_manager

# -------------------------
# KV 布局（简化、主题化）
# -------------------------
KV = r'''
#:import dp kivy.metrics.dp

<MedicationManagementScreen>:
    name: "medication_management_screen"
    canvas.before:
        Color:
            # 优先使用 app.theme（由 App 在启动时提供）
            rgba: app.theme.PRIMARY_LIGHT if hasattr(app, 'theme') else (0.95,0.97,1,1)
        Rectangle:
            pos: self.pos
            size: self.size

    MDBoxLayout:
        orientation: 'vertical'
        spacing: app.theme.DIMENSIONS.get('layout_spacing', dp(6)) if hasattr(app, 'theme') else dp(6)

        # 顶部 AppBar
        MDBoxLayout:
            size_hint_y: None
            height: app.theme.DIMENSIONS.get('appbar_height', dp(56)) if hasattr(app, 'theme') else dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR if hasattr(app, 'theme') else (0.13,0.59,0.95,1)
            padding: [app.theme.DIMENSIONS.get('appbar_padding_h', dp(8)) if hasattr(app, 'theme') else dp(8), app.theme.DIMENSIONS.get('appbar_padding_v', dp(4)) if hasattr(app, 'theme') else dp(4), app.theme.DIMENSIONS.get('appbar_padding_h', dp(8)) if hasattr(app, 'theme') else dp(8), app.theme.DIMENSIONS.get('appbar_padding_v', dp(4)) if hasattr(app, 'theme') else dp(4)]

            MDIconButton:
                icon: "arrow-left"
                theme_text_color: "Custom"
                on_release: root.go_back()

            MDLabel:
                text: "用药管理"
                halign: "center"
                font_style: "Headline"
                role: "small"
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT if hasattr(app, 'theme') else (1,1,1,1)

            MDIconButton:
                icon: "refresh"
                theme_text_color: "Custom"
                on_release: root.reload_all()

        # Logo（可选）
        HealthLogo:
            size_hint_y: None
            height: app.theme.DIMENSIONS.get('logo_height', dp(56)) if hasattr(app, 'theme') else dp(56)

        # 工具行（根据 Tab 显示）：当前用药显示“新增药物”，既往用药显示“搜索”
        # 顶部工具栏移除：按需求将“新增”与“搜索”分别移入各自Tab容器顶部

        # Tabs
        AnchorLayout:
            size_hint_y: None
            height: app.theme.DIMENSIONS.get('tab_container_height', dp(48)) if hasattr(app, 'theme') else dp(48)
            anchor_x: "center"
            anchor_y: "center"
            MDBoxLayout:
                size_hint_x: None
                width: app.theme.DIMENSIONS.get('tab_container_width', dp(240)) if hasattr(app, 'theme') else dp(240)
                spacing: app.theme.DIMENSIONS.get('tab_spacing', dp(6)) if hasattr(app, 'theme') else dp(6)
                MDButton:
                    style: "filled" if root.current_tab == 'current' else "outlined"
                    on_release: root.switch_tab('current')
                    MDButtonText:
                        text: "目前用药"
                MDButton:
                    style: "filled" if root.current_tab == 'history' else "outlined"
                    on_release: root.switch_tab('history')
                    MDButtonText:
                        text: "既往用药"

        # Add form is implemented as dialog; main content area below
        ScrollView:
            do_scroll_x: False
            do_scroll_y: True

            MDBoxLayout:
                id: main_container
                orientation: 'vertical'
                adaptive_height: True
                padding: app.theme.DIMENSIONS.get('main_container_padding', dp(8)) if hasattr(app, 'theme') else dp(8)
                spacing: app.theme.DIMENSIONS.get('main_container_spacing', dp(8)) if hasattr(app, 'theme') else dp(8)

                # 当前用药容器（动态显示）
                MDBoxLayout:
                    id: current_container
                    orientation: 'vertical'
                    adaptive_height: True

                # 既往用药容器（动态显示）
                MDBoxLayout:
                    id: history_container
                    orientation: 'vertical'
                    adaptive_height: True
'''

Builder.load_string(KV)

# -------------------------
# 卡片 Python 类（完整绑定）
# -------------------------
class BaseMedicationCard(MDCard):
    """
    Python 版基础卡片：负责构造左右布局、右侧操作按钮并暴露回调
    - card_type: 'current' 或 'history'
    - on_edit / on_remind / on_stop / on_delete 回调将由屏幕注入或由卡片直接调用 db/notification
    """

    # 绑定属性（供 KV/Factory 创建时可读写）
    name = StringProperty("")
    dosage = StringProperty("")
    frequency = StringProperty("")
    start_date = StringProperty("")
    stop_date = StringProperty("")
    reason = StringProperty("")
    notes = StringProperty("")
    row_index = NumericProperty(0)
    medication_data = ObjectProperty(None)
    is_selected = BooleanProperty(False)
    card_type = StringProperty("current")  # 'current' or 'history'
    reminder_text = StringProperty("")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 卡片风格 - 调整高度以适应更大的按钮
        self.size_hint_y = None
        app = App.get_running_app()
        dims = getattr(app.theme, 'DIMENSIONS', {}) if hasattr(app, 'theme') else {}
        self.height = dims.get('card_height', dp(100))  # 增加卡片高度以适应更大的按钮
        self.radius = [dims.get('card_radius', dp(12))]
        card_padding = dims.get('card_padding', dp(8))
        self.padding = [card_padding, card_padding, card_padding, card_padding]  # 增加内边距
        self.orientation = 'horizontal'
        self.md_bg_color = App.get_running_app().theme.SURFACE_COLOR if hasattr(App.get_running_app(), 'theme') else (1,1,1,1)

        # 左侧：信息
        self.left_box = BoxLayout(orientation='vertical', size_hint_x=0.65)
        self._build_left()

        # 右侧：按钮 - 使用垂直布局以容纳按钮
        self.right_box = BoxLayout(orientation='vertical', size_hint_x=0.35, spacing=dims.get('card_button_spacing', dp(4)))
        self._build_right_buttons()

        # 将左右加入卡片
        self.add_widget(self.left_box)
        self.add_widget(self.right_box)

        # 回调占位，屏幕会注入
        self.on_edit_callback = None
        self.on_remind_callback = None
        self.on_stop_callback = None
        self.on_delete_callback = None

    def _build_left(self):
        """构建左侧信息区域 - 修复显示内容和格式"""
        try:
            if self.left_box:
                self.left_box.clear_widgets()
        except Exception as e:
            logger.warning("[Card] _build_left清理失败: %s", e)
        
        # 药物名称（加粗显示，使用较小字体）
        title = MDLabel(
            text=f"[b]{self.name}[/b]", 
            markup=True, 
            font_style="Title", 
            role="small", 
            halign="left"
        )
        
        # 基本信息：剂量和频次（使用更小字体）
        meta = MDLabel(
            text=f"剂量: {self.dosage or '未设置'}    频次: {self.frequency or '未设置'}", 
            font_style="Body", 
            role="small", 
            halign="left"
        )
        
        # 开始时间和用药原因（使用更小字体）
        start_info = f"开始: {self.start_date or '未设置'}"
        if self.reason:
            start_info += f"    用药原因: {self.reason}"
        start_label = MDLabel(
            text=start_info, 
            font_style="Label", 
            role="small", 
            halign="left"
        )
        
        # 注意事项（如果有，使用更小字体）
        if self.notes:
            notes_label = MDLabel(
                text=f"注意事项: {self.notes}", 
                font_style="Label", 
                role="small", 
                halign="left",
                theme_text_color="Secondary"
            )
        
        # 既往用药额外显示停药信息
        stop_label = None
        if self.card_type == 'history':
            stop_info = ""
            if self.stop_date:
                stop_info = f"停药时间: {self.stop_date}"
            # 从medication_data中获取停药原因
            stop_reason = ""
            if self.medication_data and isinstance(self.medication_data, dict):
                stop_reason = self.medication_data.get('stop_reason', '')
            elif hasattr(self, 'stop_reason'):
                stop_reason = self.stop_reason

            if stop_reason:
                if stop_info:
                    stop_info += f"    停药原因: {stop_reason}"
                else:
                    stop_info = f"停药原因: {stop_reason}"
            if stop_info:
                stop_label = MDLabel(
                    text=stop_info,
                    font_style="Label",
                    role="small",
                    halign="left",
                    theme_text_color="Error"
                )
        
        # 提醒信息（使用更小字体）
        if self.reminder_text:
            reminder = MDLabel(
                text=self.reminder_text, 
                font_style="Label", 
                role="small", 
                halign="left", 
                theme_text_color="Primary"
            )
        
        # 添加所有标签到容器
        self.left_box.add_widget(title)
        self.left_box.add_widget(meta)
        self.left_box.add_widget(start_label)
        if self.notes:
            self.left_box.add_widget(notes_label)
        if self.card_type == 'history' and stop_label:
            self.left_box.add_widget(stop_label)
        if self.reminder_text:
            self.left_box.add_widget(reminder)

    def _build_right_buttons(self):
        """
        构建右侧按钮组 - 根据卡片类型显示不同按钮
        - 当前用药：提醒、停用、删除（水平排列）
        - 既往用药：只显示查询按钮（搜索功能）
        """
        try:
            if self.right_box:
                self.right_box.clear_widgets()
        except Exception as e:
            logger.warning("[Card] _build_right_buttons清理失败: %s", e)
            return
        if self.card_type == 'current':
            # 创建单行按钮布局，增加高度和间距以提高可见性
            try:
                from theme import AppTheme
                button_row_height = AppTheme.DIMENSIONS.get('button_row_height', dp(32))
            except Exception:
                button_row_height = dp(32)
            app = App.get_running_app()
            dims = getattr(app.theme, 'DIMENSIONS', {}) if hasattr(app, 'theme') else {}
            button_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=button_row_height, spacing=dims.get('button_row_spacing', dp(4)))

            # 提醒按钮 - 增大图标和按钮尺寸
            btn_remind = MDIconButton(
                icon="bell",
                icon_size=dims.get('icon_button_icon_size', dp(18)),
                size_hint=(None, None),
                size=(dims.get('icon_button_size', dp(32)), dims.get('icon_button_size', dp(32))),
                theme_icon_color="Primary",
                on_release=lambda inst: self._call_remind()
            )
            button_row.add_widget(btn_remind)

            # 停用按钮 - 增大图标和按钮尺寸
            btn_stop = MDIconButton(
                icon="stop",
                icon_size=dims.get('icon_button_icon_size', dp(18)),
                size_hint=(None, None),
                size=(dims.get('icon_button_size', dp(32)), dims.get('icon_button_size', dp(32))),
                theme_icon_color="Error",
                on_release=lambda inst: self._call_stop()
            )
            button_row.add_widget(btn_stop)

            # 删除按钮 - 增大图标和按钮尺寸
            btn_del = MDIconButton(
                icon="delete-outline",
                icon_size=dims.get('icon_button_icon_size', dp(18)),
                size_hint=(None, None),
                size=(dims.get('icon_button_size', dp(32)), dims.get('icon_button_size', dp(32))),
                theme_icon_color="Error",
                on_release=lambda inst: self._call_delete()
            )
            button_row.add_widget(btn_del)

            self.right_box.add_widget(button_row)

        elif self.card_type == 'history':
            # 既往用药不显示任何操作按钮（查询功能已移到顶部）
            pass

    # 回调触发器（先尝试使用注入的回调，否则在卡片内部处理）

    def _call_remind(self):
        try:
            if (hasattr(self, 'medication_data') and
                self.medication_data is not None and
                isinstance(self.medication_data, dict) and
                callable(self.on_remind_callback)):
                self.on_remind_callback(self.medication_data)
            else:
                toast("提醒设置暂不可用")
                logger.warning("[Card] _call_remind: medication_data无效或回调不可用")
        except Exception as e:
            logger.error("[Card] _call_remind 异常: %s", e)
            logger.error(traceback.format_exc())
            toast("提醒设置失败")

    def _call_stop(self):
        try:
            if (hasattr(self, 'medication_data') and
                self.medication_data is not None and
                isinstance(self.medication_data, dict) and
                callable(self.on_stop_callback)):
                self.on_stop_callback(self.medication_data)
            else:
                toast("停药功能暂不可用")
                logger.warning("[Card] _call_stop: medication_data无效或回调不可用")
        except Exception as e:
            logger.error("[Card] _call_stop 异常: %s", e)
            logger.error(traceback.format_exc())
            toast("停药操作失败")

    def _call_delete(self):
        try:
            if (hasattr(self, 'medication_data') and
                self.medication_data is not None and
                isinstance(self.medication_data, dict) and
                callable(self.on_delete_callback)):
                self.on_delete_callback(self.medication_data)
            else:
                toast("删除功能暂不可用")
                logger.warning("[Card] _call_delete: medication_data无效或回调不可用")
        except Exception as e:
            logger.error("[Card] _call_delete 异常: %s", e)
            logger.error(traceback.format_exc())
            toast("删除操作失败")



    # 更新显示（在属性改变后可手动调用）
    def refresh_view(self):
        try:
            self._build_left()
            self._build_right_buttons()
        except Exception as e:
            logger.error("[Card] refresh_view 异常: %s", e)
            logger.error(traceback.format_exc())

class CurrentMedicationCard(BaseMedicationCard):
    """当前用药卡片（继承基础）"""
    def __init__(self, **kwargs):
        kwargs['card_type'] = 'current'
        super().__init__(**kwargs)

class HistoryMedicationCard(BaseMedicationCard):
    """既往用药卡片 - 添加停药相关属性"""
    stop_reason = StringProperty("")  # 停药原因
    
    def __init__(self, **kwargs):
        kwargs['card_type'] = 'history'
        super().__init__(**kwargs)
        
    def get_formatted_stop_date(self) -> str:
        """格式化停药日期显示"""
        if not self.stop_date:
            return "未设置"
        try:
            # 尝试解析日期并格式化
            dt = datetime.fromisoformat(self.stop_date)
            return dt.strftime("%Y年%m月%d日")
        except Exception:
            return self.stop_date
            
    def get_stop_reason_display(self) -> str:
        """获取停药原因显示文本"""
        return self.stop_reason or "未填写"

# 注册 Python 类到 Factory（以便 KV 或 Factory.create 时使用）
Factory.register('CurrentMedicationCard', cls=CurrentMedicationCard)
Factory.register('HistoryMedicationCard', cls=HistoryMedicationCard)
Factory.register('BaseMedicationCard', cls=BaseMedicationCard)

# -------------------------
# 主屏幕类（集中业务逻辑）
# -------------------------
class MedicationManagementScreen(BaseScreen):
    """
    主屏幕：管理 UI、数据库、通知、云同步等
    - 属性：medications, history_medications, current_tab
    """

    medications = ListProperty([])
    history_medications = ListProperty([])
    current_tab = StringProperty('current')  # 'current' 或 'history'

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Screen没有orientation属性，布局由KV定义
        self.app = App.get_running_app()
        # 如果 app 没有 theme，则为其赋值（兼容 AppTheme 被导出为实例或类）
        if not hasattr(self.app, 'theme') or not getattr(self.app, 'theme', None):
            try:
                # 优先使用 ThemeManager 提供的兼容主题
                from theme import ThemeManager
                self.app.theme = ThemeManager.get_app_theme()
            except Exception:
                try:
                    # AppTheme 在 theme.py 末尾已实例化导出，直接赋值
                    self.app.theme = AppTheme
                except Exception:
                    # 保底：创建一个简单的维度配置，避免崩溃
                    from kivy.metrics import dp as _dp
                    self.app.theme = type("TmpTheme", (), {
                        "DIMENSIONS": {
                            'text_field_dialog_height': _dp(56),
                            'text_field_multiline_dialog_height': _dp(80),
                            'med_dialog_expanded_height': _dp(720),
                            'med_dialog_scroll_height': _dp(380),
                            'button_height': _dp(52),
                            'dialog_title_height': _dp(56),
                            'dialog_padding': _dp(20),
                            'dialog_spacing': _dp(20),
                            'field_spacing_medium': _dp(8),
                            'field_spacing_large': _dp(12),
                            'box_padding': _dp(16),
                        }
                    })()

        # 数据与通知管理器
        self.db = get_medication_db_manager()
        self.notification = get_notification_manager()

        # 记录选中卡片（若需要批量操作）
        self.selected_cards: List[BaseMedicationCard] = []

        # 初始化时延迟加载数据，保证 KV 已渲染
        Clock.schedule_once(lambda dt: self.reload_all(), 0.2)

        # 对话框引用（避免被回收）
        self._edit_dialog = None
        self._reminder_dialog = None
        self._confirm_dialog = None

    def _load_options_from_api_with_fallback(self):
        """从后端拉取频次/原因/注意事项选项，失败时回退默认列表。
        返回: (frequency_options, reason_options, notes_options)
        """
        freq_default = ["每日一次", "每日两次", "每日三次", "每日四次", "每周一次", "按需服用", "其他"]
        reason_default = ["控制血压", "控制血糖", "止痛/消炎", "胃部不适", "睡眠问题", "医生建议", "其他"]
        notes_default = ["饭前服用", "饭后服用", "避免与xx同服", "可能嗜睡", "可能影响驾驶", "多喝水", "其他"]
        try:
            import requests
            base = getattr(self.app, 'api_base', None) or getattr(self, 'api_base', None)
            url = f"{base}/api/medications/options"
            resp = requests.get(url, timeout=8)
            if resp.status_code != 200:
                return freq_default, reason_default, notes_default
            data = resp.json() or {}
            f = data.get('frequency') or freq_default
            r = data.get('reasons') or reason_default
            n = data.get('notes') or notes_default
            return f, r, n
        except Exception:
            return freq_default, reason_default, notes_default

    # ---------- 用户 / 环境相关 ----------
    def get_current_custom_id(self) -> Optional[str]:
        """
        获取当前用户的 custom_id
        优先从 app.user_data 中读取，若没有尝试从 utils.storage 或其他地方读取
        """
        try:
            if hasattr(self.app, 'user_data') and isinstance(self.app.user_data, dict):
                return self.app.user_data.get('custom_id')
            # 尝试 utils.storage 的回退（若项目内存在）
            try:
                from utils.storage import UserStorage
                store = UserStorage()
                return store.get('custom_id')
            except Exception:
                return None
        except Exception:
            return None

    # ---------- 加载 / 刷新 ----------
    def reload_all(self):
        """重新加载当前用药和既往用药并刷新视图"""
        user = self.get_current_custom_id()
        if not user:
            logger.info("[Screen] 未检测到用户，跳过加载")
            # 清空容器
            self._clear_containers()
            return
        # 确保默认显示目前用药Tab
        self.current_tab = 'current'
        # 清空所有容器
        self._clear_containers()
        # 只加载和显示当前用药数据（load_current_medications内部已调用refresh_current_display）
        self.load_current_medications()
        # 确保历史用药容器为空且不可见
        try:
            if hasattr(self.ids, 'history_container') and self.ids.history_container:
                self.ids.history_container.clear_widgets()
                self.ids.history_container.height = 0
                self.ids.history_container.opacity = 0
                self.ids.history_container.disabled = True
        except Exception as e:
            logger.warning("[Screen] 历史容器清理失败: %s", e)

    def _clear_containers(self):
        """清空 KV 中的容器（用于没有登录时）"""
        try:
            if hasattr(self.ids, 'current_container') and self.ids.current_container:
                self.ids.current_container.clear_widgets()
            if hasattr(self.ids, 'history_container') and self.ids.history_container:
                self.ids.history_container.clear_widgets()
        except Exception as e:
            logger.warning("[Screen] 容器清理失败: %s", e)

    def load_current_medications(self):
        """从数据库加载当前（active）用药并刷新显示"""
        user = self.get_current_custom_id()
        if not user:
            return
        try:
            meds = self.db.get_medications(user, status='active')
            self.medications = meds
            self.refresh_current_display()
        except Exception as e:
            logger.error("[Screen] load_current_medications 失败: %s", e)

    def refresh_current_display(self):
        """将当前用药渲染到 KV 的 current_container"""
        try:
            container = self.ids.get('current_container')
            if not container:
                logger.warning("[Screen] current_container不存在")
                return
            try:
                container.clear_widgets()
            except Exception as e:
                logger.warning("[Screen] current_container清理失败: %s", e)
            # 顶置一个轻量“新增”入口卡片（当前Tab内部右上角按钮/或 '+' 标签）
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.button import MDButton, MDButtonText
            try:
                from theme import AppTheme
                add_row_height = AppTheme.DIMENSIONS.get('add_row_height', dp(44))
            except Exception:
                add_row_height = dp(44)
            add_row = MDBoxLayout(orientation='horizontal', size_hint_y=None, height=add_row_height)
            add_row.add_widget(Widget())
            add_icon = MDIconButton(icon="plus", icon_size=dp(18), on_release=lambda *_: self._open_add_dialog())
            add_row.add_widget(add_icon)
            container.add_widget(add_row)
            if not self.medications:
                container.add_widget(MDLabel(text="暂无当前用药", halign="center"))
                return
            for idx, med in enumerate(self.medications):
                card = BaseMedicationCard(card_type='current')
                # 绑定数据
                card.name = med.get('name', '') or ''
                card.dosage = med.get('dosage', '') or ''
                card.frequency = med.get('frequency', '') or ''
                card.start_date = med.get('start_date', '') or ''
                card.reason = med.get('reason', '') or ''
                card.notes = med.get('notes', '') or ''
                card.row_index = idx
                card.medication_data = med
                card.reminder_text = self._build_reminder_text(med.get('reminder_settings', {}))
                # 注入回调（屏幕实现具体行为）
                card.on_edit_callback = self._open_add_dialog
                card.on_remind_callback = self._open_reminder_dialog
                card.on_stop_callback = self._prompt_stop_medication
                card.on_delete_callback = self._prompt_delete_medication
                # 确保medication_data已设置
                if not hasattr(card, 'medication_data') or card.medication_data is None:
                    card.medication_data = med
                card.refresh_view()
                container.add_widget(card)
        except Exception as e:
            logger.error("[Screen] refresh_current_display 异常: %s", e)
            logger.error(traceback.format_exc())

    def load_history_medications(self):
        """加载既往用药（stopped）"""
        user = self.get_current_custom_id()
        if not user:
            return
        try:
            meds = self.db.get_medications(user, status='stopped')
            # 按停药日期倒序
            meds_sorted = sorted(meds, key=lambda x: x.get('stop_date') or "", reverse=True)
            self.history_medications = meds_sorted
            self.refresh_history_display()
        except Exception as e:
            logger.error("[Screen] load_history_medications 失败: %s", e)

    def refresh_history_display(self):
        """渲染既往用药到 history_container"""
        try:
            container = self.ids.get('history_container')
            if not container:
                logger.warning("[Screen] history_container不存在")
                return
            try:
                container.clear_widgets()
            except Exception as e:
                logger.warning("[Screen] history_container清理失败: %s", e)

            # 顶部查询功能卡片（一行布局：输入框+查询按钮）
            from kivymd.uix.textfield import MDTextField
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.button import MDIconButton

            # 查询功能容器
            search_container = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(56),
                spacing=dp(8),
                padding=[dp(8), dp(4), dp(8), dp(4)]
            )

            # 查询输入框
            self.search_field = MDTextField(
                hint_text="按药名搜索既往用药",
                mode="outlined",
                size_hint_x=0.8
            )

            # 查询按钮
            search_button = MDIconButton(
                icon="magnify",
                icon_size=dp(24),
                size_hint_x=0.2,
                theme_icon_color="Primary",
                on_release=lambda x: self.search_history_medications(self.search_field.text)
            )

            search_container.add_widget(self.search_field)
            search_container.add_widget(search_button)
            container.add_widget(search_container)

            if not self.history_medications:
                container.add_widget(MDLabel(text="暂无既往用药", halign="center"))
                return

            for idx, med in enumerate(self.history_medications):
                card = HistoryMedicationCard()  # 使用专门的历史用药卡片
                card.name = med.get('name', '') or ''
                card.dosage = med.get('dosage', '') or ''
                card.frequency = med.get('frequency', '') or ''
                card.start_date = med.get('start_date', '') or ''
                card.stop_date = med.get('stop_date', '') or ''
                card.stop_reason = med.get('stop_reason', '') or ''  # 设置停药原因
                card.reason = med.get('reason', '') or ''
                card.notes = med.get('notes', '') or ''
                card.row_index = idx
                card.medication_data = med
                card.reminder_text = self._build_reminder_text(med.get('reminder_settings', {}))
                # 既往用药：移除所有操作按钮（包括查询按钮）
                card.on_edit_callback = None
                card.on_remind_callback = None
                card.on_stop_callback = None
                card.on_delete_callback = None
                card.on_search_callback = None  # 移除卡片中的查询功能
                card.refresh_view()
                container.add_widget(card)
        except Exception as e:
            logger.error("[Screen] refresh_history_display 异常: %s", e)

    # ---------- 卡片操作：提醒 / 停药 / 删除 ----------

    def _open_add_dialog(self):
        """
        新增药物对话框 - 修正高度配置和文本框设置
        遵循KivyMD 2.0.1 dev0规范，解决文本框超出对话框的问题
        """
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.scrollview import MDScrollView
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.textfield import MDTextField, MDTextFieldHelperText
        from kivymd.uix.button import MDButton, MDButtonText
        from kivymd.uix.menu import MDDropdownMenu
        from kivymd.uix.button import MDIconButton
        from kivy.metrics import dp
        from kivy.clock import Clock

        # 统一从App实例读取主题配置
        try:
            dims = self.app.theme.DIMENSIONS
        except Exception:
            dims = {'text_field_dialog_height': dp(56), 'text_field_multiline_dialog_height': dp(72),
                    'med_dialog_expanded_height': dp(750), 'med_dialog_scroll_height': dp(420), 'button_height': dp(52),
                    'dialog_title_height': dp(56), 'dialog_padding': dp(16), 'dialog_spacing': dp(16)}

        # 计算对话框高度（受窗口高度约束，避免过小/过大）
        from kivy.core.window import Window
        dialog_target_h = dims.get('med_dialog_expanded_height', dp(750))
        dialog_h = min(int(Window.height * 0.92), int(dialog_target_h))
        title_h = dims.get('dialog_title_height', dp(56))
        button_h = dims.get('button_height', dp(52))
        content_spacing = dims.get('dialog_spacing', dp(16))
        vertical_padding = dims.get('dialog_padding', dp(16)) * 2
        # 标题-滚动、滚动-按钮 两处间距
        spacing_total = content_spacing * 2
        scroll_h = max(dialog_h - title_h - button_h - vertical_padding - spacing_total, dp(320))

        # 使用标准的KivyMD文本框高度（限制多行高度上限，避免过高）
        field_h = dims.get('text_field_dialog_height', dp(56))
        multiline_h = min(dims.get('text_field_multiline_dialog_height', dp(72)), dp(72))

        # ScrollView 包裹 - 使用计算后的滚动区域高度
        scroll = MDScrollView(
            size_hint_y=None,
            height=scroll_h,
            do_scroll_x=False,
            do_scroll_y=True
        )
        
        # 内容容器 - 自动高度绑定，减少间距以节省空间
        box = MDBoxLayout(
            orientation="vertical", 
            spacing=dims.get('field_spacing_medium', dp(8)),  # 减少字段间距
            size_hint_y=None, 
            padding=[dims.get('box_padding', dp(16)), dims.get('field_spacing_large', dp(12))]  # 左右padding，上下较小padding
        )
        box.bind(minimum_height=box.setter("height"))

        # 创建字段 - 直接添加helper text
        # 药物名称字段（必填）
        name_field = MDTextField(
            mode="outlined", 
            size_hint_y=None, 
            height=field_h,
            hint_text="药物名称（例如：阿司匹林）*"
        )
        name_field.add_widget(MDTextFieldHelperText(text="药物名称不能为空", mode="on_error"))

        # 剂量字段（必填）
        dosage_field = MDTextField(
            mode="outlined", 
            size_hint_y=None, 
            height=field_h,
            hint_text="剂量（例如：100mg）*"
        )
        dosage_field.add_widget(MDTextFieldHelperText(text="请输入剂量", mode="on_error"))

        # 频次字段（必填）
        frequency_field = MDTextField(
            mode="outlined", 
            size_hint_y=None, 
            height=field_h,
            hint_text="用药频次（例如：每日3次）*"
        )
        frequency_field.add_widget(MDTextFieldHelperText(text="请输入用药频次", mode="on_error"))

        # 开始日期字段（必填）
        start_date_field = MDTextField(
            mode="outlined", 
            size_hint_y=None, 
            height=field_h,
            hint_text="开始日期（YYYY-MM-DD）*",
            text=datetime.now().strftime("%Y-%m-%d")
        )
        start_date_field.add_widget(MDTextFieldHelperText(text="格式 YYYY-MM-DD（必填）", mode="on_error"))

        # 用药原因字段（可选，多行）
        reason_field = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=multiline_h,
            multiline=True,
            hint_text="用药原因 / 适应症"
        )
        # 限制多行文本框的最大高度，防止“跑出”
        try:
            if hasattr(reason_field, 'max_height'):
                reason_field.max_height = max(multiline_h, dp(96))
        except Exception:
            pass
        reason_field.add_widget(MDTextFieldHelperText(text="用药原因（可选）", mode="on_focus"))

        # 注意事项字段（可选，多行）
        notes_field = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=multiline_h,
            multiline=True,
            hint_text="注意事项（如：饭前/饭后）"
        )
        try:
            if hasattr(notes_field, 'max_height'):
                notes_field.max_height = max(multiline_h, dp(96))
        except Exception:
            pass
        notes_field.add_widget(MDTextFieldHelperText(text="注意事项（可选）", mode="on_focus"))

        # 将字段添加到布局中
        box.add_widget(name_field)
        box.add_widget(dosage_field)
        box.add_widget(frequency_field)
        box.add_widget(start_date_field)
        box.add_widget(reason_field)
        box.add_widget(notes_field)

        scroll.add_widget(box)

        from kivymd.uix.dialog import MDDialogHeadlineText
        from kivymd.uix.boxlayout import MDBoxLayout
        
        def _do_add(inst):
            """添加药物的处理函数，包含必填字段验证"""
            # 验证必填字段
            name = name_field.text.strip() if name_field.text else ""
            dosage = dosage_field.text.strip() if dosage_field.text else ""
            frequency = frequency_field.text.strip() if frequency_field.text else ""
            start_date = start_date_field.text.strip() if start_date_field.text else ""
            
            # 检查必填字段
            has_error = False
            if not name:
                name_field.error = True
                has_error = True
            if not dosage:
                dosage_field.error = True
                has_error = True
            if not frequency:
                frequency_field.error = True
                has_error = True
            if not start_date:
                start_date_field.error = True
                has_error = True

            if has_error:
                return

            med = {
                'name': name,
                'dosage': dosage,
                'frequency': frequency,
                'start_date': start_date,
                'reason': reason_field.text.strip() if reason_field.text else '',
                'notes': notes_field.text.strip() if notes_field.text else ''
            }
            try:
                saved = self.db.save_medication(custom_id=self.get_current_custom_id() or "", medication=med)
                toast("已添加药物")
                self.reload_all()
            except ValueError as ve:
                # 处理重复药物错误
                logger.warning("重复药物: %s", ve)
                toast(str(ve))
                return
            except Exception as e:
                logger.error("save_medication 异常: %s", e)
                toast("保存失败")
            finally:
                self.add_dialog.dismiss()
        
        # 创建对话框 - 使用KivyMD 2.0.1 dev0格式
        from kivymd.uix.button import MDButton, MDButtonText
        from kivymd.uix.dialog import MDDialogHeadlineText
        from kivymd.uix.boxlayout import MDBoxLayout
        
        # 创建按钮容器
        button_container = MDBoxLayout(
            orientation='horizontal',
            spacing=dims.get('button_spacing', dp(12)),
            adaptive_width=True,
            size_hint_y=None,
            height=dims.get('button_height', dp(52))
        )
        
        # 取消按钮
        cancel_button = MDButton(
            MDButtonText(text="取消"),
            style="outlined",
            on_release=lambda x: self.add_dialog.dismiss()
        )
        
        # 保存按钮
        save_button = MDButton(
            MDButtonText(text="保存"),
            style="filled",
            on_release=_do_add
        )
        
        button_container.add_widget(cancel_button)
        button_container.add_widget(save_button)
        
        # 创建对话框内容容器 - 固定高度子布局，防止撑破
        content_container = MDBoxLayout(
            orientation='vertical',
            spacing=dims.get('dialog_spacing', dp(16)),
            size_hint_y=None,
            padding=dims.get('dialog_padding', dp(16))
        )
        content_container.bind(minimum_height=content_container.setter('height'))

        # 添加标题
        title_label = MDDialogHeadlineText(
            text="新增药物",
            halign="center"
        )

        # 组装内容容器
        content_container.add_widget(title_label)
        # 让滚动区吸收多余空间，按钮固定高度
        scroll.size_hint_y = None
        content_container.add_widget(scroll)
        button_container.size_hint_y = None
        content_container.add_widget(button_container)
        # 固定容器高度，防止随内容无限增高
        try:
            content_container.height = dialog_h
        except Exception:
            pass

        # 创建对话框 - 使用扩展高度并居中显示
        self.add_dialog = MDDialog(
            content_container,
            size_hint=(0.9, None),
            height=dialog_h,
            pos_hint={'center_x': 0.5, 'center_y': 0.5}  # 垂直居中显示
        )

        self.add_dialog.open()



    def _open_reminder_dialog(self, med_data: Dict[str, Any]):
        """
        设置提醒对话框 - 简化版本
        """
        from kivy.clock import Clock
        from kivy.metrics import dp
        from kivymd.uix.textfield import MDTextField, MDTextFieldHelperText
        from kivymd.uix.scrollview import MDScrollView
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.button import MDButton, MDButtonText, MDIconButton
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.label import MDLabel
        from utils.review_reminder_tool import calc_next_review_time

        # 统一从App实例读取主题配置
        try:
            dims = self.app.theme.DIMENSIONS
        except Exception:
            try:
                from theme import ThemeManager
                dims = ThemeManager.get_app_theme().DIMENSIONS
            except Exception:
                dims = {'text_field_dialog_height': dp(56), 'text_field_multiline_dialog_height': dp(80), 
                       'med_dialog_expanded_height': dp(580), 'med_dialog_scroll_height': dp(300), 'button_height': dp(52)}

        # 计算对话框高度布局
        dialog_h = dims.get('med_dialog_expanded_height', dp(720))
        title_h = dims.get('dialog_title_height', dp(56))
        button_h = dims.get('button_height', dp(52))
        padding = dims.get('dialog_padding', dp(20)) * 2 + dims.get('dialog_spacing', dp(20))  # 上下padding + spacing
        scroll_h = dialog_h - title_h - button_h - padding
        
        field_h = dims.get('text_field_dialog_height', dp(56))
        row_h = dims.get('text_field_dialog_height', dp(56))

        # 创建内容容器
        scroll = MDScrollView(
            size_hint_y=None,
            height=scroll_h
        )
        box = MDBoxLayout(
            orientation='vertical',
            spacing=dims.get('field_spacing_large', dp(12)),
            padding=dims.get('box_padding', dp(16)),
            size_hint_y=None
        )

        # 用药提醒时间字段
        times_box = MDBoxLayout(
            orientation='vertical',
            spacing=dims.get('field_spacing_medium', dp(8))
        )
        time_fields = []

        def add_time_field(prefill: str = ""):
            if len(time_fields) >= 4:
                toast("最多设置4个时间段")
                return
            row = MDBoxLayout(
                orientation='horizontal',
                spacing=dims.get('field_spacing_medium', dp(8)),
                size_hint_y=None,
                height=row_h
            )
            tf = MDTextField(
                text=prefill,
                hint_text="HH:MM",
                mode="outlined",
                size_hint_y=None,
                height=field_h
            )
            
            # 直接添加 helper，避免延迟调用
            tf.add_widget(MDTextFieldHelperText(text="输入提醒时间 (HH:MM)", mode="on_focus"))
            
            del_btn = MDIconButton(icon="close", icon_size=dims.get('icon_size_small', dp(18)))
            def remove_row(*_):
                times_box.remove_widget(row)
                time_fields.remove(tf)
            del_btn.bind(on_release=remove_row)
            row.add_widget(tf)
            row.add_widget(del_btn)
            times_box.add_widget(row)
            time_fields.append(tf)

        # 预填已有设置
        exist = (med_data.get('reminder_settings') or {}).get('med_reminder', {})
        for t in (exist.get('times') or [])[:4]:
            if isinstance(t, dict):
                add_time_field(t.get('time', ''))
            else:
                add_time_field(str(t))
        if not time_fields:
            add_time_field()

        # 新增时间段按钮
        add_time_btn = MDButton(
            MDButtonText(text="新增时间段"),
            style="outlined"
        )
        add_time_btn.bind(on_release=lambda *_: add_time_field())

        # 复查提醒字段
        review_weeks_field = MDTextField(
            text="4",
            hint_text="复查周数（整数）",
            mode="outlined",
            size_hint_y=None,
            height=field_h
        )
        
        # 直接添加 helper，避免延迟调用
        review_weeks_field.add_widget(MDTextFieldHelperText(text="输入复查间隔周数", mode="on_focus"))
        
        # 复查预览标签
        review_preview = MDLabel(
            text="下一次复查：--", 
            font_style="Label", 
            role="small",
            halign="center"
        )

        def update_review_preview(*_):
            try:
                weeks = int((review_weeks_field.text or '4').strip())
                dt = calc_next_review_time(weeks=weeks)
                review_preview.text = f"下一次复查：{dt.strftime('%Y-%m-%d')}"
            except Exception:
                review_preview.text = "下一次复查：--"

        review_weeks_field.bind(text=update_review_preview)
        update_review_preview()

        # 复查提醒行
        review_row = MDBoxLayout(
            orientation='horizontal',
            spacing=dims.get('field_spacing_medium', dp(8)),
            size_hint_y=None,
            height=row_h
        )
        review_row.add_widget(review_weeks_field)
        review_row.add_widget(review_preview)

        # 添加标题标签
        med_reminder_label = MDLabel(
            text="用药提醒", 
            font_style="Title", 
            role="small",
            halign="center"
        )
        
        review_reminder_label = MDLabel(
            text="复查提醒", 
            font_style="Title", 
            role="small",
            halign="center"
        )
        
        box.add_widget(med_reminder_label)
        box.add_widget(times_box)
        box.add_widget(add_time_btn)
        box.add_widget(review_reminder_label)
        box.add_widget(review_row)

        # 设置 box 自动高度绑定
        box.bind(minimum_height=box.setter("height"))
        scroll.add_widget(box)

        from kivymd.uix.dialog import MDDialogHeadlineText
        from kivymd.uix.boxlayout import MDBoxLayout
        
        def _do_save(inst):
            # 汇总用药提醒并校验
            import re
            hhmm = re.compile(r"^(?:[01]\d|2[0-3]):[0-5]\d$")
            times = []
            seen = set()
            for tf in time_fields:
                val = (tf.text or '').strip()
                if not val:
                    continue
                if not hhmm.match(val):
                    toast(f"时间格式错误: {val}，需 HH:MM")
                    return
                if val in seen:
                    continue
                seen.add(val)
                times.append({"time": val, "advance_minutes": "15"})
            med_rem = {"enabled": True if times else False, "times": times}
            # 复查
            try:
                weeks = int((review_weeks_field.text or '4').strip())
            except Exception:
                weeks = 4
            next_review = calc_next_review_time(weeks=weeks).strftime("%Y-%m-%d")
            review_rem = {"enabled": True, "date": next_review, "advance_days": "3"}
            # 保存到提醒表
            custom_id = self.get_current_custom_id()
            if not custom_id:
                toast("未登录，无法保存提醒")
                return
            ok_all = True
            ok1 = self.db.save_reminder(med_data.get('id'), custom_id,
                                        {"reminder_time": json.dumps({"times": times}, ensure_ascii=False), "reminder_type": "daily"})
            ok_all = ok_all and ok1
            ok2 = self.db.save_reminder(med_data.get('id'), custom_id,
                                        {"reminder_time": json.dumps({"date": next_review, "advance_days": "3"}, ensure_ascii=False), "reminder_type": "review"})
            ok_all = ok_all and ok2

            dlg.dismiss()
            if ok_all:
                toast("提醒保存成功")
                try:
                    updated = self.db.get_medication_by_id(med_data.get('id'))
                    self._auto_sync_event('reminder', updated)
                except Exception:
                    pass
                # 如设置了具体时间段，安排一次最近一次的本地提醒
                if times:
                    first_time = times[0].get('time')
                    notify_dt = self._parse_time_text_to_datetime_from_field(first_time)
                    if notify_dt:
                        try:
                            self.notification.schedule_medication_reminder(med_data.get('id'), med_data.get('name'), notify_dt)
                        except Exception as e:
                            logger.error("[Screen] 安排提醒失败：%s", e)
                self.reload_all()
            else:
                toast("提醒保存失败")
        
        # 创建对话框内容容器
        content_container = MDBoxLayout(
            orientation='vertical',
            spacing=dp(20),
            size_hint_y=None,
            height=dialog_h,
            padding=dp(20)
        )
        
        # 添加标题
        title_label = MDDialogHeadlineText(
            text=f"设置提醒 - {med_data.get('name')}",
            halign="center"
        )
        
        # 添加按钮容器
        button_container = MDBoxLayout(
            orientation='horizontal',
            spacing=dims.get('button_spacing', dp(12)),
            adaptive_width=True,
            size_hint_y=None,
            height=dims.get('button_height', dp(52))
        )
        
        # 取消按钮
        cancel_button = MDButton(
            MDButtonText(text="取消"), 
            style="outlined", 
            on_release=lambda x: dlg.dismiss()
        )
        
        # 保存按钮
        save_button = MDButton(
            MDButtonText(text="保存"), 
            style="filled", 
            on_release=_do_save
        )
        
        button_container.add_widget(cancel_button)
        button_container.add_widget(save_button)
        
        # 组装内容容器
        content_container.add_widget(title_label)
        content_container.add_widget(scroll)
        content_container.add_widget(button_container)
        
        dlg = MDDialog(
            content_container,
            size_hint=(0.9, None),
            height=dialog_h
        )

        dlg.open()
        self._reminder_dialog = dlg

    def _prompt_stop_medication(self, med_data: Dict[str, Any]):
        """停用药物 - 简化版本：包含停用原因（下拉）与停用日期（默认当天）"""
        try:
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.textfield import MDTextField
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.menu import MDDropdownMenu
            from kivymd.uix.scrollview import MDScrollView

            # 统一从App实例读取主题配置
            try:
                dims = self.app.theme.DIMENSIONS
            except Exception:
                try:
                    from theme import ThemeManager
                    dims = ThemeManager.get_app_theme().DIMENSIONS
                except Exception:
                    dims = {'text_field_dialog_height': dp(40), 'text_field_multiline_dialog_height': dp(64), 
                           'med_dialog_compact_height': dp(580), 'button_height': dp(52), 'med_dialog_scroll_height': dp(300)}

            # 计算对话框高度布局
            dialog_h = dims.get('med_dialog_compact_height', dp(650))
            title_h = dims.get('dialog_title_height', dp(56))
            button_h = dims.get('button_height', dp(52))
            padding = dims.get('dialog_padding', dp(20)) * 2 + dims.get('dialog_spacing', dp(20))  # 上下padding + spacing
            scroll_h = dialog_h - title_h - button_h - padding
            
            field_h = dims.get('text_field_dialog_height', dp(56))
            multiline_h = dims.get('text_field_multiline_dialog_height', dp(64))

            # 创建滚动视图
            scroll = MDScrollView(
                size_hint_y=None,
                height=scroll_h,
                do_scroll_x=False,
                do_scroll_y=True
            )

            box = MDBoxLayout(
                orientation='vertical',
                spacing=dims.get('field_spacing_large', dp(12)),
                size_hint_y=None,
                padding=[dims.get('box_padding', dp(16)), dims.get('box_padding', dp(16)), dims.get('box_padding', dp(16)), dims.get('box_padding', dp(16))]
            )

            # 停用原因（多行输入 + 选择菜单图标）
            reason_row = MDBoxLayout(
                orientation='horizontal',
                spacing=dims.get('field_spacing', dp(4)),
                size_hint_y=None,
                height=multiline_h
            )
            reason_field = MDTextField(
                hint_text="请输入停用原因（如：疗程结束、不良反应等）",
                mode='outlined',
                multiline=True,
                size_hint_y=None,
                height=multiline_h
            )
            from kivymd.uix.textfield import MDTextFieldHelperText
            reason_field.add_widget(MDTextFieldHelperText(
                text="输入停用原因或点击右侧图标选择",
                mode="on_focus"
            ))
            from kivymd.uix.button import MDIconButton
            reason_menu_btn = MDIconButton(icon='menu-down', icon_size=dims.get('icon_size_small', dp(20)))
            reason_row.add_widget(reason_field)
            reason_row.add_widget(reason_menu_btn)

            # 停用日期
            date_field = MDTextField(
                text=datetime.now().strftime("%Y-%m-%d"),
                hint_text="停用日期 (YYYY-MM-DD)",
                mode='outlined',
                size_hint_y=None,
                height=field_h
            )
            date_field.add_widget(MDTextFieldHelperText(
                text="输入停用日期",
                mode="on_focus"
            ))

            box.add_widget(reason_row)
            box.add_widget(date_field)

            # 设置 box 自动高度绑定
            box.bind(minimum_height=box.setter("height"))
            
            # 将内容添加到滚动容器
            scroll.add_widget(box)

            # 停用原因：点击右侧图标弹出建议菜单，支持选择+自定义
            reasons = ["疗程结束", "不良反应", "效果不佳", "医生指示", "经济原因", "其他"]
            def open_reason_menu(*_):
                items = [{"text": opt, "on_release": (lambda x=opt: (setattr(reason_field, 'text', x), menu.dismiss()))} for opt in reasons]
                menu_height = dims.get('dropdown_menu_height', dp(200))
                menu_width = dims.get('dropdown_menu_width', dp(240))
                menu = MDDropdownMenu(caller=reason_menu_btn, items=items, width=menu_width, max_height=menu_height, position="bottom")
                menu.open()
            reason_menu_btn.on_release = open_reason_menu

            def do_stop(*args):
                reason = (reason_field.text or "").strip()
                stop_date = (date_field.text or datetime.now().strftime("%Y-%m-%d")).strip()
                if not reason:
                    toast("请选择停用原因")
                    return
                ok = self.db.update_medication_status(med_data.get('id'), 'stopped', stop_date=stop_date, stop_reason=reason)
                dlg.dismiss()
                if ok:
                    toast("已停用")
                    try:
                        stopped = self.db.get_medication_by_id(med_data.get('id'))
                        self._auto_sync_event('stop', stopped)
                    except Exception:
                        pass
                    self.load_current_medications()
                    self.load_history_medications()
                else:
                    toast("停药失败")

            # 创建按钮容器
            button_container = MDBoxLayout(
                orientation='horizontal',
                spacing=dims.get('button_spacing', dp(12)),
                adaptive_width=True,
                size_hint_y=None,
                height=dims.get('button_height', dp(52))
            )
            
            # 取消按钮
            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="outlined",
                on_release=lambda x: dlg.dismiss()
            )
            
            # 停用按钮
            stop_button = MDButton(
                MDButtonText(text="停用"),
                style="filled",
                on_release=lambda x: do_stop()
            )
            
            button_container.add_widget(cancel_button)
            button_container.add_widget(stop_button)

            # 创建对话框内容容器
            content_container = MDBoxLayout(
                orientation='vertical',
                spacing=dims.get('dialog_spacing', dp(20)),
                size_hint_y=None,
                padding=dims.get('dialog_padding', dp(20))
            )
            
            # 添加标题
            from kivymd.uix.dialog import MDDialogHeadlineText
            title_label = MDDialogHeadlineText(
                text=f"停用药物 - {med_data.get('name')}",
                halign="center"
            )
            
            # 组装内容容器
            content_container.add_widget(title_label)
            content_container.add_widget(scroll)
            content_container.add_widget(button_container)
            
            # 设置容器高度
            content_container.height = dialog_h
            
            dlg = MDDialog(
                content_container,
                size_hint=(0.9, None),
                height=dialog_h
            )
            dlg.open()
            self._confirm_dialog = dlg
        except Exception as e:
            logger.error("[Screen] _prompt_stop_medication 异常: %s", e)

    def _prompt_delete_medication(self, med_data: Dict[str, Any]):
        """弹窗确认删除"""
        try:
            def do_delete(*args):
                ok = self.db.delete_medication(med_data.get('id'))
                dlg.dismiss()
                if ok:
                    toast("删除成功")
                    try:
                        self._auto_sync_event('delete', med_data)
                    except Exception:
                        pass
                    self.reload_all()
                else:
                    toast("删除失败")

            # 统一从App实例读取主题配置
            try:
                dims = self.app.theme.DIMENSIONS
            except Exception:
                try:
                    from theme import ThemeManager
                    dims = ThemeManager.get_app_theme().DIMENSIONS
                except Exception:
                    dims = {'confirm_dialog_height': dp(250), 'confirm_dialog_container_height': dp(200), 
                           'confirm_label_height': dp(60), 'button_height': dp(52)}
            
            dialog_height = dims.get('confirm_dialog_height', dp(250))

            from kivymd.uix.dialog import MDDialogHeadlineText
            from kivymd.uix.boxlayout import MDBoxLayout

            # 创建按钮容器
            button_container = MDBoxLayout(
                orientation='horizontal',
                spacing=dims.get('button_spacing', dp(12)),
                adaptive_width=True,
                size_hint_y=None,
                height=dims.get('button_height', dp(52))
            )
            
            # 取消按钮
            cancel_btn = MDButton(
                MDButtonText(text="取消"),
                style="outlined", 
                on_release=lambda x: dlg.dismiss()
            )
            
            # 删除按钮
            del_btn = MDButton(
                MDButtonText(text="删除"),
                style="filled", 
                on_release=lambda x: do_delete()
            )
            
            button_container.add_widget(cancel_btn)
            button_container.add_widget(del_btn)

            # 创建对话框内容容器
            content_container = MDBoxLayout(
                orientation='vertical',
                spacing=dims.get('dialog_spacing', dp(20)),
                size_hint_y=None,
                padding=dims.get('dialog_padding', dp(20))
            )
            
            # 添加标题
            title_label = MDDialogHeadlineText(
                text=f"删除 - {med_data.get('name')}",
                halign="center"
            )
            
            # 添加确认文本
            from kivymd.uix.label import MDLabel
            confirm_label_height = dims.get('confirm_label_height', dp(60))
            confirm_label = MDLabel(
                text="确定删除该药物记录？此操作不可恢复。",
                halign="center",
                size_hint_y=None,
                height=confirm_label_height
            )
            
            # 组装内容容器
            content_container.add_widget(title_label)
            content_container.add_widget(confirm_label)
            content_container.add_widget(button_container)
            
            # 设置容器高度
            container_height = dims.get('confirm_dialog_container_height', dp(200))
            content_container.height = container_height

            dlg = MDDialog(
                content_container,
                size_hint=(0.9, None),
                height=dialog_height
            )
            dlg.open()
            self._confirm_dialog = dlg
        except Exception as e:
            logger.error("[Screen] _prompt_delete_medication 异常: %s", e)

    # ---------- 自动同步工具（增强：离线队列与退避重试） ----------
    def _auto_sync_event(self, action: str, med: Dict[str, Any]):
        """在本地数据变更后触发自动同步。
        - 支持离线队列合并（同 record_id 仅保留一次）
        - 失败指数退避重试
        """
        try:
            from utils.sync_service import get_sync_manager
            from utils.cloud_api import get_cloud_api
            from utils.offline_sync import OfflineQueue

            # 安全获取同步管理器
            try:
                sm = get_sync_manager()
                api = get_cloud_api()
            except Exception as e:
                logger.warning("[Sync] 同步服务不可用: %s", e)
                return

            user = self.get_current_custom_id()
            if not user:
                logger.warning("[Sync] 用户ID不可用")
                return

            # 安全设置同步管理器
            try:
                sm.set_cloud_api(api)
                sm.set_user_id(user)
            except Exception as e:
                logger.warning("[Sync] 同步管理器配置失败: %s", e)
                return
            record_id = med.get('id') if isinstance(med, dict) else None
            table_name = 'medications'
            if not record_id:
                return
            # 入离线队列（合并）
            q = OfflineQueue()
            q.enqueue(user, table_name, record_id, action, payload={"updated_at": med.get('updated_at')})

            # 立即尝试一次同步
            def _try_sync(dt=None):
                task = q.pop()
                if not task:
                    return
                ok = False
                try:
                    sm.queue_sync_task(user, table_name, task['record_id'], priority=0)
                    sm.sync_now(user)
                    ok = True
                except Exception:
                    ok = False
                if not ok:
                    # 退避重试
                    task['retry'] = int(task.get('retry', 0)) + 1
                    delay = OfflineQueue.next_delay(task['retry'])
                    q.push_front(task)
                    Clock.schedule_once(_try_sync, delay)

            Clock.schedule_once(_try_sync, 0)
        except Exception as e:
            logger.error("[Sync] 自动同步排队失败: %s", e)

    # ---------- 搜索（支持拼音/模糊） ----------
    def search_history_medications(self, query: str):
        """在已加载的 history_medications 列表中过滤显示（支持拼音/模糊）"""
        q = (query or "").strip().lower()
        if not q:
            self.refresh_history_display()
            return
        results = []
        try:
            def abbr(s: str) -> str:
                try:
                    import pypinyin
                    from pypinyin import Style
                    arr = pypinyin.pinyin(s, style=Style.FIRST_LETTER)
                    return ''.join(x[0] for x in arr if x)
                except Exception:
                    return ''
            for m in self.history_medications:
                name = (m.get('name') or '')
                stop_reason = (m.get('stop_reason') or '')
                if q in name.lower() or q in stop_reason.lower():
                    results.append(m)
                    continue
                if q and abbr(name).startswith(q):
                    results.append(m)
            # 渲染过滤结果
            container = self.ids.get('history_container')
            if not container:
                logger.warning("[Screen] history_container不存在")
                return
            try:
                container.clear_widgets()
            except Exception as e:
                logger.warning("[Screen] 搜索结果容器清理失败: %s", e)
                return

            # 重新添加顶部查询功能卡片
            from kivymd.uix.textfield import MDTextField
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.button import MDIconButton

            # 查询功能容器
            search_container = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(56),
                spacing=dp(8),
                padding=[dp(8), dp(4), dp(8), dp(4)]
            )

            # 查询输入框（保持搜索内容）
            self.search_field = MDTextField(
                hint_text="按药名搜索既往用药",
                mode="outlined",
                size_hint_x=0.8,
                text=query  # 保持搜索内容
            )

            # 查询按钮
            search_button = MDIconButton(
                icon="magnify",
                icon_size=dp(24),
                size_hint_x=0.2,
                theme_icon_color="Primary",
                on_release=lambda x: self.search_history_medications(self.search_field.text)
            )

            search_container.add_widget(self.search_field)
            search_container.add_widget(search_button)
            container.add_widget(search_container)

            if not results:
                container.add_widget(MDLabel(text=f"未找到包含 '{query}' 的既往用药", halign="center"))
                return

            for idx, med in enumerate(results):
                card = HistoryMedicationCard()
                card.name = med.get('name', '')
                card.dosage = med.get('dosage', '')
                card.frequency = med.get('frequency', '')
                card.start_date = med.get('start_date', '')
                card.stop_date = med.get('stop_date', '')
                card.stop_reason = med.get('stop_reason', '')  # 设置停药原因
                card.reason = med.get('reason', '')
                card.notes = med.get('notes', '')
                card.row_index = idx
                card.medication_data = med
                card.reminder_text = self._build_reminder_text(med.get('reminder_settings', {}))
                # 既往用药搜索结果：移除所有操作按钮
                card.on_edit_callback = None
                card.on_remind_callback = None
                card.on_stop_callback = None
                card.on_delete_callback = None
                card.on_search_callback = None  # 移除卡片中的查询功能
                card.refresh_view()
                container.add_widget(card)
        except Exception as e:
            logger.error("[Screen] search_history_medications 异常: %s", e)



    # ---------- 辅助方法 ----------
    def _build_reminder_text(self, reminder_settings: Dict[str, Any]) -> str:
        """把 reminder_settings 转为短文本显示"""
        texts = []
        med_rem = (reminder_settings or {}).get('med_reminder', {}) or {}
        if med_rem.get('enabled'):
            times = med_rem.get('times', [])
            if times:
                ts = []
                for t in times:
                    if isinstance(t, dict):
                        tm = t.get('time', '')
                        adv = t.get('advance_minutes', '15')
                    else:
                        tm = t or ''
                        adv = '15'
                    if tm:
                        ts.append(f"{tm}(提前{adv}分)")
                if ts:
                    texts.append("服药：" + ", ".join(ts))
        rev = (reminder_settings or {}).get('review_reminder', {}) or {}
        if rev.get('enabled') and rev.get('date'):
            texts.append(f"复查：{rev.get('date')}（提前{rev.get('advance_days','3')}天）")
        return " | ".join(texts)

    def _parse_time_text_to_datetime_from_field(self, time_text: str) -> Optional[datetime]:
        """
        将用户输入的提醒时间文本解析为 datetime（用于安排一次示例性提醒）
        支持：
         - ISO 格式日期时间
         - HH:MM（解释为当天或次日最近的该时间）
        """
        if not time_text:
            return None
        try:
            # 如果 JSON（times 数组），尝试取第一个 time 字段
            if (time_text.startswith('[') or time_text.startswith('{')):
                parsed = json.loads(time_text)
                # 支持 {'times': [...] } 或直接 list
                candidate = None
                if isinstance(parsed, dict) and 'times' in parsed and len(parsed['times']) > 0:
                    candidate = parsed['times'][0].get('time')
                elif isinstance(parsed, list) and len(parsed) > 0:
                    if isinstance(parsed[0], dict):
                        candidate = parsed[0].get('time')
                    else:
                        candidate = parsed[0]
                if candidate:
                    time_text = candidate
            # ISO
            try:
                dt = datetime.fromisoformat(time_text)
                return dt
            except Exception:
                pass
            # HH:MM
            if ':' in time_text:
                now = datetime.now()
                parts = time_text.split(':')
                hour = int(parts[0])
                minute = int(parts[1]) if len(parts) > 1 else 0
                candidate = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
                if candidate <= now:
                    candidate = candidate + timedelta(days=1)
                return candidate
            return None
        except Exception as e:
            logger.error("[Screen] _parse_time_text_to_datetime_from_field 异常: %s", e)
            return None

    def switch_tab(self, tab_name: str):
        """
        切换 Tab 并控制哪些容器显示 - 修复问题2：确保只显示选中的tab内容
        """
        self.current_tab = tab_name
        # KV 中两个容器都存在，但我们可以通过清空/填充来模拟切换
        try:
            if tab_name == 'current':
                # 清空 history 展示，刷新 current
                if hasattr(self.ids, 'history_container') and self.ids.history_container:
                    self.ids.history_container.clear_widgets()
                self.refresh_current_display()
            else:
                # 清空 current 展示，加载并刷新 history
                if hasattr(self.ids, 'current_container') and self.ids.current_container:
                    self.ids.current_container.clear_widgets()
                self.load_history_medications()  # 确保加载历史数据
        except Exception as e:
            logger.error("[Screen] switch_tab失败: %s", e)

    def _parse_iso_date(self, text: str) -> str:
        """简单校验 ISO 日期，若无法解析返回空"""
        try:
            datetime.fromisoformat(text)
            return text
        except Exception:
            return ""

    def go_back(self):
        """返回上级屏幕实现（尝试切换 root）"""
        try:
            app = App.get_running_app()
            if hasattr(app, 'root'):
                app.root.current = 'health_data_management_screen'
        except Exception:
            pass



# 如果这个模块被直接运行，可做简单的调试 App（仅用于本地测试）
if __name__ == "__main__":
    from kivymd.app import MDApp
    from kivy.uix.screenmanager import Screen, ScreenManager

    class TestApp(MDApp):
        def build(self):
            self.theme_cls.material_style = "M3"
            # 将 AppTheme 赋给 app（KV 使用）
            try:
                self.theme = AppTheme()
            except Exception:
                pass
            sm = ScreenManager()
            screen = Screen(name='med_test')
            root = Builder.load_string("""
BoxLayout:
    orientation: 'vertical'
    MedicationManagementScreen:
""")
            screen.add_widget(root)
            sm.add_widget(screen)
            return sm

    def show_frequency_menu(self, text_field):
        """显示使用频次下拉菜单"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            frequency_options = [
                "每日一次", "每日两次", "每日三次", "每日四次",
                "每周一次", "每周两次", "每周三次",
                "每月一次", "按需服用", "其他"
            ]

            menu_items = []
            for option in frequency_options:
                menu_items.append({
                    "text": option,
                    "on_release": lambda x=option: self.set_frequency(x, text_field)
                })

            try:
                from theme import AppTheme
                menu_max_height = AppTheme.DIMENSIONS.get('dropdown_menu_height', dp(200))
            except Exception:
                menu_max_height = dp(200)
            self.frequency_menu = MDDropdownMenu(
                caller=text_field,
                items=menu_items,
                width=dp(240),
                max_height=menu_max_height,
            )
            self.frequency_menu.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示频次菜单失败: {e}")

    def set_frequency(self, frequency, text_field):
        """设置使用频次"""
        try:
            text_field.text = frequency
            if hasattr(self, 'frequency_menu') and self.frequency_menu:
                self.frequency_menu.dismiss()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 设置频次失败: {e}")

    def show_reason_menu(self, text_field):
        """显示用药原因下拉菜单"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            reason_options = [
                "高血压", "糖尿病", "高血脂", "冠心病",
                "心律不齐", "慢性肾病", "甲状腺疾病",
                "关节炎", "骨质疏松", "抑郁症", "其他原因"
            ]

            menu_items = []
            for option in reason_options:
                menu_items.append({
                    "text": option,
                    "on_release": lambda x=option: self.set_reason(x, text_field)
                })

            try:
                from theme import AppTheme
                menu_max_height = AppTheme.DIMENSIONS.get('dropdown_menu_height', dp(200))
            except Exception:
                menu_max_height = dp(200)
            self.reason_menu = MDDropdownMenu(
                caller=text_field,
                items=menu_items,
                width=dp(240),
                max_height=menu_max_height,
            )
            self.reason_menu.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示原因菜单失败: {e}")

    def set_reason(self, reason, text_field):
        """设置用药原因"""
        try:
            text_field.text = reason
            if hasattr(self, 'reason_menu') and self.reason_menu:
                self.reason_menu.dismiss()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 设置原因失败: {e}")

    def show_notes_menu(self, text_field):
        """显示注意事项下拉菜单"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            notes_options = [
                "空腹服用", "餐前服用", "餐中服用", "餐后服用",
                "睡前服用", "避免饮酒", "多饮水", "无特殊要求"
            ]

            menu_items = []
            for option in notes_options:
                menu_items.append({
                    "text": option,
                    "on_release": lambda x=option: self.set_notes(x, text_field)
                })

            try:
                from theme import AppTheme
                menu_max_height = AppTheme.DIMENSIONS.get('dropdown_menu_height', dp(200))
            except Exception:
                menu_max_height = dp(200)
            self.notes_menu = MDDropdownMenu(
                caller=text_field,
                items=menu_items,
                width=dp(240),
                max_height=menu_max_height,
            )
            self.notes_menu.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示注意事项菜单失败: {e}")

    def set_notes(self, notes, text_field):
        """设置注意事项"""
        try:
            text_field.text = notes
            if hasattr(self, 'notes_menu') and self.notes_menu:
                self.notes_menu.dismiss()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 设置注意事项失败: {e}")

    def show_date_picker(self, text_field):
        """显示日期选择器"""
        try:
            from utils.date_picker_utils import show_medication_start_date_picker
            show_medication_start_date_picker(text_field)
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示日期选择器失败: {e}")

    def _search_medication_info(self, medication_data: Dict[str, Any]):
        """查询药物详细信息的方法"""
        try:
            if not medication_data or not isinstance(medication_data, dict):
                toast("药物信息无效")
                return

            med_name = medication_data.get('name', '未知药物')
            
            # 创建药物信息显示对话框
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.label import MDLabel
            
            # 构建药物详细信息文本
            info_lines = [
                f"药物名称: {med_name}",
                f"剂量: {medication_data.get('dosage', '未记录')}",
                f"频次: {medication_data.get('frequency', '未记录')}",
                f"开始日期: {medication_data.get('start_date', '未记录')}",
            ]
            
            if medication_data.get('stop_date'):
                info_lines.append(f"停药日期: {medication_data.get('stop_date')}")
            
            if medication_data.get('stop_reason'):
                info_lines.append(f"停药原因: {medication_data.get('stop_reason')}")
                
            if medication_data.get('reason'):
                info_lines.append(f"用药原因: {medication_data.get('reason')}")
                
            if medication_data.get('notes'):
                info_lines.append(f"注意事项: {medication_data.get('notes')}")
            
            info_text = "\n".join(info_lines)
            
            # 创建对话框内容
            content = MDBoxLayout(
                orientation="vertical",
                adaptive_height=True,
                spacing=dp(8),
                padding=[dp(16), dp(8), dp(16), dp(8)]
            )
            
            info_label = MDLabel(
                text=info_text,
                theme_text_color="Primary",
                halign="left",
                adaptive_height=True,
                text_size=(dp(280), None)
            )
            content.add_widget(info_label)
            
            # 创建按钮容器
            button_container = MDDialogButtonContainer()
            close_button = MDButton(
                style="text",
                on_release=lambda x: self._close_search_dialog()
            )
            close_button.add_widget(MDButtonText(text="关闭"))
            button_container.add_widget(close_button)
            
            # 创建对话框
            self.search_dialog = MDDialog(
                MDDialogHeadlineText(text=f"药物信息 - {med_name}"),
                MDDialogSupportingText(text=""),
                content,
                button_container,
                size_hint=(0.9, None),
                height=dp(400)
            )
            
            self.search_dialog.open()
            
        except Exception as e:
            logger.error(f"[Screen] _search_medication_info 异常: {e}")
            logger.error(traceback.format_exc())
            toast("查询药物信息失败")
    
    def _close_search_dialog(self):
        """关闭药物信息查询对话框"""
        try:
            if hasattr(self, 'search_dialog') and self.search_dialog:
                self.search_dialog.dismiss()
                self.search_dialog = None
        except Exception as e:
            logger.error(f"[Screen] _close_search_dialog 异常: {e}")
