#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试认证函数
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

import asyncio
from fastapi import Request
from unittest.mock import Mock
from sqlalchemy.orm import Session

# 导入必要的模块
try:
    from app.core.db_connection import get_db_session
    from app.api.endpoints.auth import mobile_login
    print("✅ 成功导入认证模块")
except Exception as e:
    print(f"❌ 导入认证模块失败: {e}")
    sys.exit(1)

async def test_mobile_login():
    """
    直接测试移动端登录函数
    """
    print("\n=== 开始测试移动端登录函数 ===")
    
    try:
        # 创建模拟的请求对象
        mock_request = Mock(spec=Request)
        
        # 模拟JSON数据
        async def mock_json():
            return {
                "username": "admin",
                "password": "admin123"
            }
        
        mock_request.json = mock_json
        
        # 获取数据库会话
        db = get_db_session()
        
        print("✅ 数据库连接成功")
        
        # 调用登录函数
        result = await mobile_login(mock_request, db)
        
        print(f"✅ 登录函数调用成功")
        print(f"结果: {result}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            db.close()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(test_mobile_login())