from __future__ import annotations
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

"""
复查提醒工具（可被多处脚本调用）
- 提供创建/更新/取消复查提醒的统一接口
- 可根据药品信息与复查频率生成下一次提醒时间
- 注意：具体的本地通知发送由通知管理器（如 utils/notification.py）完成
"""

DEFAULT_REVIEW_WEEKS = 4


def calc_next_review_time(base: Optional[datetime] = None, weeks: int = DEFAULT_REVIEW_WEEKS) -> datetime:
    """计算下一次复查提醒时间"""
    base = base or datetime.now()
    return base + timedelta(weeks=weeks)


def build_review_payload(med: Dict[str, Any], weeks: int = DEFAULT_REVIEW_WEEKS) -> Dict[str, Any]:
    """构造复查提醒的存储结构"""
    return {
        "med_id": med.get("id"),
        "name": med.get("name"),
        "review_weeks": weeks,
        "next_review_at": calc_next_review_time().isoformat(),
        "created_at": datetime.now().isoformat(),
    }


def apply_review_setting(db, med_id: int, weeks: int = DEFAULT_REVIEW_WEEKS) -> bool:
    """将复查提醒设置保存到数据库。db 需提供 update_medication_review_settings 接口。"""
    try:
        payload = {
            "review_weeks": weeks,
            "next_review_at": calc_next_review_time().isoformat(),
        }
        return db.update_medication_review_settings(med_id, payload)
    except Exception:
        return False

