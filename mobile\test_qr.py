#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试二维码生成功能
"""

import qrcode
from PIL import Image, ImageDraw
import json
import time

def test_basic_qr():
    """测试基本二维码生成"""
    print("=== 测试基本二维码生成 ===")
    try:
        qr = qrcode.QRCode(
            version=3,
            error_correction=qrcode.constants.ERROR_CORRECT_H,
            box_size=10,
            border=4
        )
        
        qr.add_data("test data")
        qr.make(fit=True)
        
        # 测试基本生成
        img = qr.make_image(fill_color="black", back_color="white")
        print(f"基本生成成功: {img is not None}")
        print(f"图像类型: {type(img)}")
        print(f"图像尺寸: {img.size if img else 'None'}")
        
        if img:
            # 测试转换
            rgba_img = img.convert('RGBA')
            print(f"RGBA转换成功: {rgba_img is not None}")
            print(f"RGBA图像尺寸: {rgba_img.size if rgba_img else 'None'}")
            
            return rgba_img
        
    except Exception as e:
        print(f"基本生成失败: {e}")
        import traceback
        traceback.print_exc()
    
    return None

def test_color_qr():
    """测试彩色二维码生成"""
    print("\n=== 测试彩色二维码生成 ===")
    try:
        qr = qrcode.QRCode(
            version=3,
            error_correction=qrcode.constants.ERROR_CORRECT_H,
            box_size=10,
            border=4
        )
        
        qr.add_data("test color data")
        qr.make(fit=True)
        
        # 测试RGB颜色
        color = (32, 184, 64)  # 绿色
        color_hex = "#{:02x}{:02x}{:02x}".format(color[0], color[1], color[2])
        print(f"颜色转换: {color} -> {color_hex}")
        
        img = qr.make_image(fill_color=color_hex, back_color="white")
        print(f"彩色生成成功: {img is not None}")
        print(f"图像类型: {type(img)}")
        
        if img:
            rgba_img = img.convert('RGBA')
            print(f"彩色RGBA转换成功: {rgba_img is not None}")
            return rgba_img
            
    except Exception as e:
        print(f"彩色生成失败: {e}")
        import traceback
        traceback.print_exc()
    
    return None

def test_complex_data():
    """测试复杂数据二维码生成"""
    print("\n=== 测试复杂数据二维码生成 ===")
    try:
        # 模拟健康数据
        health_data = {
            "user_id": "SM_008",
            "timestamp": int(time.time()),
            "blood_pressure": {"systolic": 120, "diastolic": 80},
            "blood_glucose": {"value": 5.5},
            "heart_rate": 72
        }
        
        qr_data = {
            "type": "health_data",
            "data": health_data,
            "expiry": int(time.time()) + 120,
            "app_prefix": "HEALTH_TREA:"
        }
        
        data_str = "HEALTH_TREA:" + json.dumps(qr_data, ensure_ascii=False)
        print(f"数据长度: {len(data_str)}")
        
        qr = qrcode.QRCode(
            version=3,
            error_correction=qrcode.constants.ERROR_CORRECT_H,
            box_size=10,
            border=4
        )
        
        qr.add_data(data_str)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="#20b840", back_color="white")
        print(f"复杂数据生成成功: {img is not None}")
        
        if img:
            rgba_img = img.convert('RGBA')
            print(f"复杂数据RGBA转换成功: {rgba_img is not None}")
            return rgba_img
            
    except Exception as e:
        print(f"复杂数据生成失败: {e}")
        import traceback
        traceback.print_exc()
    
    return None

def test_pil_image():
    """测试PIL图像创建"""
    print("\n=== 测试PIL图像创建 ===")
    try:
        # 测试基本图像创建
        img = Image.new('RGBA', (200, 200), (255, 255, 255, 255))
        print(f"PIL图像创建成功: {img is not None}")
        print(f"图像尺寸: {img.size if img else 'None'}")
        
        if img:
            # 测试绘制
            draw = ImageDraw.Draw(img)
            print(f"ImageDraw创建成功: {draw is not None}")
            
            if draw:
                draw.rectangle([10, 10, 190, 190], outline=(255, 0, 0, 255), width=3)
                print("绘制矩形成功")
                
                try:
                    draw.text((50, 90), "Test", fill=(255, 0, 0, 255))
                    print("绘制文本成功")
                except Exception as text_e:
                    print(f"绘制文本失败: {text_e}")
                    
            return img
            
    except Exception as e:
        print(f"PIL图像创建失败: {e}")
        import traceback
        traceback.print_exc()
    
    return None

if __name__ == "__main__":
    print("开始二维码生成测试...")
    
    # 测试基本功能
    basic_img = test_basic_qr()
    color_img = test_color_qr()
    complex_img = test_complex_data()
    pil_img = test_pil_image()
    
    print("\n=== 测试结果汇总 ===")
    print(f"基本二维码: {'成功' if basic_img else '失败'}")
    print(f"彩色二维码: {'成功' if color_img else '失败'}")
    print(f"复杂数据二维码: {'成功' if complex_img else '失败'}")
    print(f"PIL图像创建: {'成功' if pil_img else '失败'}")
    
    if all([basic_img, color_img, complex_img, pil_img]):
        print("\n所有测试通过！二维码生成功能正常。")
    else:
        print("\n部分测试失败，需要进一步调试。")